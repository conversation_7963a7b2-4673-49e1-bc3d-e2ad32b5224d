# CSP 高级颜色管理插件 - 用户指南

## 📖 目录
1. [安装说明](#安装说明)
2. [界面介绍](#界面介绍)
3. [基础功能](#基础功能)
4. [高级功能](#高级功能)
5. [实用技巧](#实用技巧)
6. [常见问题](#常见问题)

## 🔧 安装说明

### 系统要求
- Clip Studio Paint (支持插件的版本)
- Windows 操作系统
- Visual C++ 运行库

### 安装步骤
1. 编译插件生成 `ColorManager_Plugin.dll`
2. 将 DLL 文件复制到 CSP 插件目录
3. 重启 Clip Studio Paint
4. 在滤镜菜单中找到"高级颜色管理器"

## 🎨 界面介绍

### 主要控制区域

#### 1. **颜色模式选择**
- **RGB**: 红绿蓝三基色模式，适用于数字显示
- **HSB**: 色相饱和度亮度模式，更直观易用
- **CMYK**: 青品黄黑印刷模式，适用于印刷输出
- **Lab**: 设备无关颜色空间，专业级精确度

#### 2. **颜色调整滑块**
- **RGB模式**: 红色(0-255)、绿色(0-255)、蓝色(0-255)
- **HSB模式**: 色相(0-360°)、饱和度(0-1)、亮度(0-1)

#### 3. **颜色和谐选择**
- **互补色**: 色轮上180度对面的颜色
- **邻近色**: 色轮上相邻的颜色
- **三角色**: 120度等间距的三色组合

#### 4. **调色盘区域**
- 8个颜色槽位，可存储常用颜色
- 点击槽位可快速选择颜色
- 支持自动生成和谐色调色盘

#### 5. **历史记录区域**
- 显示最近使用的5个颜色
- 支持撤销/重做操作
- 自动记录颜色变化

## 🎯 基础功能

### 颜色选择
1. **直接调整**: 使用滑块直接调整RGB或HSB值
2. **和谐色生成**: 选择和谐类型，自动生成配色方案
3. **调色盘选择**: 点击调色盘中的颜色快速选择

### 颜色模式切换
1. 在颜色模式下拉菜单中选择所需模式
2. 滑块会自动切换到对应的控制方式
3. 颜色值会自动转换，保持视觉一致性

### 调色盘管理
1. **添加颜色**: 当前颜色会自动添加到调色盘
2. **排序**: 使用排序按钮按色相、亮度或饱和度排序
3. **清空**: 一键清空所有调色盘颜色
4. **预设**: 选择预设调色盘快速加载主题色彩

## 🚀 高级功能

### 颜色约束系统

#### 亮度锁定
- **功能**: 锁定颜色的亮度值，只调整色相和饱和度
- **使用**: 勾选"Luminance Lock"，调整亮度值滑块
- **应用**: 保持画面整体亮度一致性

#### RYB模式
- **功能**: 切换到传统艺术家色轮(红黄蓝)
- **使用**: 勾选"RYB Mode"
- **应用**: 更适合传统绘画的配色思维

### 颜色分析工具

#### 对比度分析
- **显示**: 当前颜色与白色背景的对比度比值
- **标准**: 基于WCAG 2.1可访问性标准
- **应用**: 确保文字和背景的可读性

#### 色温显示
- **显示**: 当前颜色的色温值(开尔文)
- **范围**: 2000K(暖色) - 10000K(冷色)
- **应用**: 控制画面的冷暖色调平衡

### 历史记录系统
- **容量**: 最多记录50个颜色的使用历史
- **撤销**: 点击撤销按钮回到上一个颜色
- **重做**: 点击重做按钮前进到下一个颜色
- **智能去重**: 自动避免记录重复的颜色

## 💡 实用技巧

### 配色技巧

#### 1. **单色配色**
- 选择基础色
- 使用HSB模式调整亮度和饱和度
- 创建同色系的深浅变化

#### 2. **互补色配色**
- 选择主色调
- 切换到"互补色"和谐模式
- 获得强烈对比的配色方案

#### 3. **三角配色**
- 选择基础色
- 使用"三角色"和谐模式
- 获得平衡且丰富的配色方案

### 工作流程优化

#### 1. **快速配色**
1. 选择预设调色盘作为起点
2. 使用和谐色生成相关配色
3. 微调个别颜色以符合需求
4. 保存到自定义调色盘

#### 2. **色彩一致性**
1. 启用亮度锁定
2. 设置合适的亮度值
3. 只调整色相和饱和度
4. 确保整体画面亮度统一

#### 3. **专业印刷**
1. 切换到CMYK模式
2. 检查色域范围
3. 调整颜色以适应印刷要求
4. 预览最终效果

## ❓ 常见问题

### Q: 为什么颜色在不同模式下看起来不同？
A: 不同颜色模式有不同的色域范围。RGB适用于屏幕显示，CMYK适用于印刷，转换时可能出现细微差异。

### Q: 如何保存自定义调色盘？
A: 当前版本的调色盘会在会话期间保持，但不会永久保存。建议记录重要的颜色值。

### Q: 亮度锁定不起作用怎么办？
A: 确保已勾选"Luminance Lock"选项，并设置了合适的亮度值。某些极端颜色可能无法完全锁定亮度。

### Q: 如何获得最佳的颜色对比度？
A: 使用对比度分析功能，确保对比度比值达到4.5:1(普通文本)或3:1(大文本)以符合可访问性标准。

### Q: 预设调色盘如何使用？
A: 在预设调色盘下拉菜单中选择主题，调色盘会自动加载对应的颜色组合。

## 🎨 预设调色盘说明

- **Material**: 材质色调，适用于工业设计
- **Nature**: 自然色调，适用于风景绘画
- **Warm**: 暖色调，营造温暖氛围
- **Cool**: 冷色调，营造清爽氛围
- **Monochrome**: 单色调，黑白灰渐变
- **Vintage**: 复古色调，怀旧风格
- **Neon**: 霓虹色调，现代科技感
- **Pastel**: 柔和色调，温柔清新
- **Autumn**: 秋季色调，温暖丰收
- **Spring**: 春季色调，清新活力

## 📞 技术支持

如遇到问题或需要帮助，请参考：
1. 插件文档和示例
2. CSP官方插件开发指南
3. 颜色理论相关资料

---

**版本**: v1.2.0  
**更新日期**: 2024年  
**兼容性**: Clip Studio Paint (支持插件版本)
