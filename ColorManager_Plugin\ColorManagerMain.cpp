#include "TriglavPlugInSDK/TriglavPlugInSDK.h"
#include "ColorManager.h"
#include <iostream>

// 属性项ID定义
static const int kItemColorMode = 1;           // 颜色模式选择
static const int kItemHarmonyType = 2;         // 和谐类型选择

// RGB控件
static const int kItemRedSlider = 10;
static const int kItemGreenSlider = 11;
static const int kItemBlueSlider = 12;

// HSB控件
static const int kItemHueSlider = 20;
static const int kItemSaturationSlider = 21;
static const int kItemBrightnessSlider = 22;

// CMYK控件
static const int kItemCyanSlider = 30;
static const int kItemMagentaSlider = 31;
static const int kItemYellowSlider = 32;
static const int kItemKeySlider = 33;

// Lab控件
static const int kItemLSlider = 40;
static const int kItemASlider = 41;
static const int kItemBSlider = 42;

// 约束控件
static const int kItemLuminanceLock = 50;
static const int kItemLuminanceValue = 51;
static const int kItemGamutLock = 52;
static const int kItemSaturationLimit = 53;
static const int kItemRYBMode = 54;

// 调色盘槽位
static const int kItemPaletteSlot1 = 100;
static const int kItemPaletteSlot2 = 101;
static const int kItemPaletteSlot3 = 102;
static const int kItemPaletteSlot4 = 103;
static const int kItemPaletteSlot5 = 104;
static const int kItemPaletteSlot6 = 105;
static const int kItemPaletteSlot7 = 106;
static const int kItemPaletteSlot8 = 107;

// 预览控件
static const int kItemPreview = 200;

// 字符串ID
static const int kStringIDFilterCategoryName = 101;
static const int kStringIDFilterName = 102;
static const int kStringIDColorMode = 103;
static const int kStringIDHarmonyType = 104;
static const int kStringIDRGBRed = 105;
static const int kStringIDRGBGreen = 106;
static const int kStringIDRGBBlue = 107;
static const int kStringIDHSBHue = 108;
static const int kStringIDHSBSaturation = 109;
static const int kStringIDHSBBrightness = 110;
static const int kStringIDPreview = 111;

// 插件UUID
static const char* uuidOfThisPlugin = "2F9C8497-302C-4AD3-9BBC-44F6C87EA9BF";

// 插件数据结构
struct ColorManagerInfo {
    ColorManager* manager;
    TriglavPlugInPropertyService* propertyService;
    bool preview;
};

// 属性回调函数
static void TRIGLAV_PLUGIN_CALLBACK ColorManagerPropertyCallBack(
    TriglavPlugInInt* result, 
    TriglavPlugInPropertyObject propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt notify, 
    TriglavPlugInPtr data) {
    
    *result = kTriglavPlugInPropertyCallBackResultNoModify;
    
    ColorManagerInfo* info = static_cast<ColorManagerInfo*>(data);
    if (info == nullptr || info->manager == nullptr || info->propertyService == nullptr) {
        return;
    }
    
    if (notify == kTriglavPlugInPropertyCallBackNotifyValueChanged) {
        ColorManager* manager = info->manager;
        TriglavPlugInPropertyService* propService = info->propertyService;
        
        // 处理不同的属性变化
        if (itemKey == kItemColorMode) {
            TriglavPlugInInt mode;
            propService->getEnumerationValueProc(&mode, propertyObject, kItemColorMode);
            manager->SetColorMode(static_cast<ColorMode>(mode));
        }
        else if (itemKey == kItemHarmonyType) {
            TriglavPlugInInt harmonyType;
            propService->getEnumerationValueProc(&harmonyType, propertyObject, kItemHarmonyType);
            manager->SetHarmonyType(static_cast<HarmonyType>(harmonyType));
        }
        else if (itemKey >= kItemRedSlider && itemKey <= kItemBlueSlider) {
            // RGB滑块变化
            TriglavPlugInInt r, g, b;
            propService->getIntegerValueProc(&r, propertyObject, kItemRedSlider);
            propService->getIntegerValueProc(&g, propertyObject, kItemGreenSlider);
            propService->getIntegerValueProc(&b, propertyObject, kItemBlueSlider);
            
            TriglavPlugInRGBColor newColor;
            newColor.red = static_cast<TriglavPlugInUInt8>(r);
            newColor.green = static_cast<TriglavPlugInUInt8>(g);
            newColor.blue = static_cast<TriglavPlugInUInt8>(b);
            
            manager->SetCurrentColor(newColor);
        }
        else if (itemKey >= kItemHueSlider && itemKey <= kItemBrightnessSlider) {
            // HSB滑块变化
            TriglavPlugInDouble h, s, br;
            propService->getDecimalValueProc(&h, propertyObject, kItemHueSlider);
            propService->getDecimalValueProc(&s, propertyObject, kItemSaturationSlider);
            propService->getDecimalValueProc(&br, propertyObject, kItemBrightnessSlider);
            
            HSBColor hsb;
            hsb.hue = static_cast<float>(h);
            hsb.saturation = static_cast<float>(s);
            hsb.brightness = static_cast<float>(br);
            
            TriglavPlugInRGBColor newColor = ColorSpaceConverter::HSBtoRGB(hsb);
            manager->SetCurrentColor(newColor);
        }
        else if (itemKey == kItemLuminanceLock) {
            TriglavPlugInBool locked;
            propService->getBooleanValueProc(&locked, propertyObject, kItemLuminanceLock);
            
            TriglavPlugInDouble luminance = 0.5;
            if (locked) {
                propService->getDecimalValueProc(&luminance, propertyObject, kItemLuminanceValue);
            }
            
            manager->GetConstraints().SetLuminanceLock(locked != 0, static_cast<float>(luminance));
        }
        else if (itemKey == kItemRYBMode) {
            TriglavPlugInBool rybMode;
            propService->getBooleanValueProc(&rybMode, propertyObject, kItemRYBMode);
            manager->GetConstraints().SetRYBMode(rybMode != 0);
        }
        
        // 检查预览状态
        TriglavPlugInBool previewEnabled;
        propService->getBooleanValueProc(&previewEnabled, propertyObject, kItemPreview);
        
        if (previewEnabled) {
            // 触发预览更新
            *result = kTriglavPlugInPropertyCallBackResultModify;
        }
    }
}

// 创建UI属性
void CreateColorManagerUI(TriglavPlugInPropertyObject propertyObject, 
                         TriglavPlugInPropertyService* propertyService,
                         TriglavPlugInStringService* stringService,
                         TriglavPlugInHostObject hostObject) {
    
    // 颜色模式选择
    TriglavPlugInStringObject colorModeCaption = nullptr;
    stringService->createWithStringIDProc(&colorModeCaption, kStringIDColorMode, hostObject);
    propertyService->addItemProc(propertyObject, kItemColorMode, 
        kTriglavPlugInPropertyValueTypeEnumeration, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, colorModeCaption, 'M');
    
    // 添加颜色模式选项
    TriglavPlugInStringObject rgbOption = nullptr;
    stringService->createWithAsciiStringProc(&rgbOption, "RGB", 3);
    propertyService->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_RGB, rgbOption, 'R');
    stringService->releaseProc(rgbOption);
    
    TriglavPlugInStringObject hsbOption = nullptr;
    stringService->createWithAsciiStringProc(&hsbOption, "HSB", 3);
    propertyService->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_HSB, hsbOption, 'H');
    stringService->releaseProc(hsbOption);
    
    TriglavPlugInStringObject cmykOption = nullptr;
    stringService->createWithAsciiStringProc(&cmykOption, "CMYK", 4);
    propertyService->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_CMYK, cmykOption, 'C');
    stringService->releaseProc(cmykOption);
    
    TriglavPlugInStringObject labOption = nullptr;
    stringService->createWithAsciiStringProc(&labOption, "Lab", 3);
    propertyService->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_LAB, labOption, 'L');
    stringService->releaseProc(labOption);
    
    propertyService->setEnumerationValueProc(propertyObject, kItemColorMode, MODE_RGB);
    stringService->releaseProc(colorModeCaption);
    
    // 和谐类型选择
    TriglavPlugInStringObject harmonyCaption = nullptr;
    stringService->createWithStringIDProc(&harmonyCaption, kStringIDHarmonyType, hostObject);
    propertyService->addItemProc(propertyObject, kItemHarmonyType, 
        kTriglavPlugInPropertyValueTypeEnumeration, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, harmonyCaption, 'H');
    
    // 添加和谐类型选项
    TriglavPlugInStringObject compOption = nullptr;
    stringService->createWithAsciiStringProc(&compOption, "Complementary", 13);
    propertyService->addEnumerationItemProc(propertyObject, kItemHarmonyType, HARMONY_COMPLEMENTARY, compOption, '1');
    stringService->releaseProc(compOption);
    
    TriglavPlugInStringObject anaOption = nullptr;
    stringService->createWithAsciiStringProc(&anaOption, "Analogous", 9);
    propertyService->addEnumerationItemProc(propertyObject, kItemHarmonyType, HARMONY_ANALOGOUS, anaOption, '2');
    stringService->releaseProc(anaOption);
    
    TriglavPlugInStringObject triOption = nullptr;
    stringService->createWithAsciiStringProc(&triOption, "Triadic", 7);
    propertyService->addEnumerationItemProc(propertyObject, kItemHarmonyType, HARMONY_TRIADIC, triOption, '3');
    stringService->releaseProc(triOption);
    
    propertyService->setEnumerationValueProc(propertyObject, kItemHarmonyType, HARMONY_COMPLEMENTARY);
    stringService->releaseProc(harmonyCaption);
    
    // RGB滑块
    TriglavPlugInStringObject redCaption = nullptr;
    stringService->createWithStringIDProc(&redCaption, kStringIDRGBRed, hostObject);
    propertyService->addItemProc(propertyObject, kItemRedSlider, 
        kTriglavPlugInPropertyValueTypeInteger, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, redCaption, 'r');
    propertyService->setIntegerMinValueProc(propertyObject, kItemRedSlider, 0);
    propertyService->setIntegerMaxValueProc(propertyObject, kItemRedSlider, 255);
    propertyService->setIntegerValueProc(propertyObject, kItemRedSlider, 255);
    stringService->releaseProc(redCaption);
    
    TriglavPlugInStringObject greenCaption = nullptr;
    stringService->createWithStringIDProc(&greenCaption, kStringIDRGBGreen, hostObject);
    propertyService->addItemProc(propertyObject, kItemGreenSlider, 
        kTriglavPlugInPropertyValueTypeInteger, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, greenCaption, 'g');
    propertyService->setIntegerMinValueProc(propertyObject, kItemGreenSlider, 0);
    propertyService->setIntegerMaxValueProc(propertyObject, kItemGreenSlider, 255);
    propertyService->setIntegerValueProc(propertyObject, kItemGreenSlider, 255);
    stringService->releaseProc(greenCaption);
    
    TriglavPlugInStringObject blueCaption = nullptr;
    stringService->createWithStringIDProc(&blueCaption, kStringIDRGBBlue, hostObject);
    propertyService->addItemProc(propertyObject, kItemBlueSlider, 
        kTriglavPlugInPropertyValueTypeInteger, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, blueCaption, 'b');
    propertyService->setIntegerMinValueProc(propertyObject, kItemBlueSlider, 0);
    propertyService->setIntegerMaxValueProc(propertyObject, kItemBlueSlider, 255);
    propertyService->setIntegerValueProc(propertyObject, kItemBlueSlider, 255);
    stringService->releaseProc(blueCaption);
    
    // 亮度锁定
    TriglavPlugInStringObject lumLockCaption = nullptr;
    stringService->createWithAsciiStringProc(&lumLockCaption, "Luminance Lock", 14);
    propertyService->addItemProc(propertyObject, kItemLuminanceLock, 
        kTriglavPlugInPropertyValueTypeBoolean, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, lumLockCaption, 'L');
    propertyService->setBooleanValueProc(propertyObject, kItemLuminanceLock, false);
    stringService->releaseProc(lumLockCaption);
    
    // RYB模式
    TriglavPlugInStringObject rybCaption = nullptr;
    stringService->createWithAsciiStringProc(&rybCaption, "RYB Mode", 8);
    propertyService->addItemProc(propertyObject, kItemRYBMode, 
        kTriglavPlugInPropertyValueTypeBoolean, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, rybCaption, 'Y');
    propertyService->setBooleanValueProc(propertyObject, kItemRYBMode, false);
    stringService->releaseProc(rybCaption);
    
    // 预览控件
    TriglavPlugInStringObject previewCaption = nullptr;
    stringService->createWithStringIDProc(&previewCaption, kStringIDPreview, hostObject);
    propertyService->addItemProc(propertyObject, kItemPreview,
        kTriglavPlugInPropertyValueTypeBoolean, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, previewCaption, 'P');
    propertyService->setBooleanValueProc(propertyObject, kItemPreview, true);
    stringService->releaseProc(previewCaption);
}

// 主插件入口函数
void TRIGLAV_PLUGIN_API TriglavPluginCall(
    TriglavPlugInInt* result,
    TriglavPlugInPtr* data,
    TriglavPlugInInt selector,
    TriglavPlugInServer* pluginServer,
    TriglavPlugInPtr reserved) {

    *result = kTriglavPlugInCallResultFailed;

    if (pluginServer == nullptr) return;

    if (selector == kTriglavPlugInSelectorModuleInitialize) {
        // 模块初始化
        *data = new ColorManagerInfo();
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        info->manager = new ColorManager();
        info->propertyService = nullptr;
        info->preview = true;

        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorModuleTerminate) {
        // 模块终止
        if (*data != nullptr) {
            ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
            if (info->manager != nullptr) {
                delete info->manager;
            }
            delete info;
            *data = nullptr;
        }
        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorFilterInitialize) {
        // 滤镜初始化
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        if (info == nullptr || info->manager == nullptr) return;

        TriglavPlugInRecordSuite* recordSuite = &pluginServer->recordSuite;
        TriglavPlugInPropertyService* propertyService = pluginServer->serviceSuite.propertyService;
        TriglavPlugInStringService* stringService = pluginServer->serviceSuite.stringService;
        TriglavPlugInHostObject hostObject = pluginServer->hostObject;

        if (propertyService == nullptr || stringService == nullptr) return;

        info->propertyService = propertyService;

        // 创建属性对象
        TriglavPlugInPropertyObject propertyObject = nullptr;
        propertyService->createProc(&propertyObject);

        if (propertyObject == nullptr) return;

        // 创建UI
        CreateColorManagerUI(propertyObject, propertyService, stringService, hostObject);

        // 初始化颜色管理器
        info->manager->Initialize(propertyService, propertyObject);

        // 设置属性和回调
        TriglavPlugInFilterInitializeSetProperty(recordSuite, hostObject, propertyObject);
        TriglavPlugInFilterInitializeSetPropertyCallBack(recordSuite, hostObject,
            ColorManagerPropertyCallBack, *data);

        // 释放属性对象
        propertyService->releaseProc(propertyObject);

        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorFilterTerminate) {
        // 滤镜终止
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        if (info != nullptr && info->manager != nullptr) {
            info->manager->Shutdown();
        }
        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorFilterRun) {
        // 滤镜运行
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        if (info == nullptr || info->manager == nullptr) return;

        TriglavPlugInRecordSuite* recordSuite = &pluginServer->recordSuite;
        TriglavPlugInHostObject hostObject = pluginServer->hostObject;

        if (TriglavPlugInGetFilterRunRecord(recordSuite) == nullptr) return;

        // 主循环
        while (true) {
            TriglavPlugInInt processResult;
            TriglavPlugInFilterRunProcess(recordSuite, &processResult, hostObject,
                kTriglavPlugInFilterRunProcessStateEnd);

            if (processResult == kTriglavPlugInFilterRunProcessResultRestart) {
                // 重新开始
                continue;
            }
            else if (processResult == kTriglavPlugInFilterRunProcessResultExit) {
                // 退出
                break;
            }
        }

        *result = kTriglavPlugInCallResultSuccess;
    }
}
