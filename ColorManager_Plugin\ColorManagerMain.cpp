#include "TriglavPlugInSDK/TriglavPlugInSDK.h"
#include "ColorManager.h"
#include <iostream>

// 属性项ID定义
static const int kItemColorMode = 1;           // 颜色模式选择
static const int kItemHarmonyType = 2;         // 和谐类型选择

// RGB控件
static const int kItemRedSlider = 10;
static const int kItemGreenSlider = 11;
static const int kItemBlueSlider = 12;

// HSB控件
static const int kItemHueSlider = 20;
static const int kItemSaturationSlider = 21;
static const int kItemBrightnessSlider = 22;

// CMYK控件
static const int kItemCyanSlider = 30;
static const int kItemMagentaSlider = 31;
static const int kItemYellowSlider = 32;
static const int kItemKeySlider = 33;

// Lab控件
static const int kItemLSlider = 40;
static const int kItemASlider = 41;
static const int kItemBSlider = 42;

// 约束控件
static const int kItemLuminanceLock = 50;
static const int kItemLuminanceValue = 51;
static const int kItemGamutLock = 52;
static const int kItemSaturationLimit = 53;
static const int kItemRYBMode = 54;

// 调色盘槽位
static const int kItemPaletteSlot1 = 100;
static const int kItemPaletteSlot2 = 101;
static const int kItemPaletteSlot3 = 102;
static const int kItemPaletteSlot4 = 103;
static const int kItemPaletteSlot5 = 104;
static const int kItemPaletteSlot6 = 105;
static const int kItemPaletteSlot7 = 106;
static const int kItemPaletteSlot8 = 107;

// 历史记录控件
static const int kItemUndoColor = 150;
static const int kItemRedoColor = 151;
static const int kItemHistorySlot1 = 160;
static const int kItemHistorySlot2 = 161;
static const int kItemHistorySlot3 = 162;
static const int kItemHistorySlot4 = 163;
static const int kItemHistorySlot5 = 164;

// 颜色分析控件
static const int kItemContrastRatio = 170;
static const int kItemAccessibilityCheck = 171;
static const int kItemColorBlindTest = 172;
static const int kItemColorTemperature = 173;

// 采样控件
static const int kItemSamplePoint = 180;
static const int kItemSampleArea = 181;

// 调色盘操作
static const int kItemPaletteSortHue = 190;
static const int kItemPaletteSortBrightness = 191;
static const int kItemPaletteSortSaturation = 192;
static const int kItemPaletteClear = 193;
static const int kItemPresetPalette = 194;

// 预览控件
static const int kItemPreview = 200;

// 字符串ID
static const int kStringIDFilterCategoryName = 101;
static const int kStringIDFilterName = 102;
static const int kStringIDColorMode = 103;
static const int kStringIDHarmonyType = 104;
static const int kStringIDRGBRed = 105;
static const int kStringIDRGBGreen = 106;
static const int kStringIDRGBBlue = 107;
static const int kStringIDHSBHue = 108;
static const int kStringIDHSBSaturation = 109;
static const int kStringIDHSBBrightness = 110;
static const int kStringIDPreview = 111;
static const int kStringIDUndo = 112;
static const int kStringIDRedo = 113;
static const int kStringIDColorHistory = 114;
static const int kStringIDColorAnalysis = 115;
static const int kStringIDSampleColor = 116;
static const int kStringIDPaletteOperations = 117;

// 插件UUID
static const char* uuidOfThisPlugin = "2F9C8497-302C-4AD3-9BBC-44F6C87EA9BF";

// 插件数据结构
struct ColorManagerInfo {
    ColorManager* manager;
    TriglavPlugInPropertyService* propertyService;
    TriglavPlugInPropertyService2* propertyService2;
    TriglavPlugInStringService* stringService;
    TriglavPlugInOffscreenService* offscreenService;
    bool preview;
};

// 属性回调函数
static void TRIGLAV_PLUGIN_CALLBACK ColorManagerPropertyCallBack(
    TriglavPlugInInt* result, 
    TriglavPlugInPropertyObject propertyObject, 
    const TriglavPlugInInt itemKey, 
    const TriglavPlugInInt notify, 
    TriglavPlugInPtr data) {
    
    *result = kTriglavPlugInPropertyCallBackResultNoModify;
    
    ColorManagerInfo* info = static_cast<ColorManagerInfo*>(data);
    if (info == nullptr || info->manager == nullptr || info->propertyService == nullptr) {
        return;
    }

    if (notify == kTriglavPlugInPropertyCallBackNotifyValueChanged) {
        ColorManager* manager = info->manager;
        TriglavPlugInPropertyService* propService = info->propertyService;
        TriglavPlugInPropertyService2* propService2 = info->propertyService2;
        
        // 处理不同的属性变化
        if (itemKey == kItemColorMode) {
            TriglavPlugInInt mode;
            propService2->getEnumerationValueProc(propertyObject, kItemColorMode, &mode);
            manager->SetColorMode(static_cast<ColorMode>(mode));
        }
        else if (itemKey == kItemHarmonyType) {
            TriglavPlugInInt harmonyType;
            propService2->getEnumerationValueProc(propertyObject, kItemHarmonyType, &harmonyType);
            manager->SetHarmonyType(static_cast<HarmonyType>(harmonyType));
        }
        else if (itemKey >= kItemRedSlider && itemKey <= kItemBlueSlider) {
            // RGB滑块变化
            TriglavPlugInInt r, g, b;
            propService->getIntegerValueProc(propertyObject, kItemRedSlider, &r);
            propService->getIntegerValueProc(propertyObject, kItemGreenSlider, &g);
            propService->getIntegerValueProc(propertyObject, kItemBlueSlider, &b);

            TriglavPlugInRGBColor newColor;
            newColor.red = static_cast<TriglavPlugInUInt8>(r);
            newColor.green = static_cast<TriglavPlugInUInt8>(g);
            newColor.blue = static_cast<TriglavPlugInUInt8>(b);

            manager->SetCurrentColor(newColor);
        }
        else if (itemKey >= kItemHueSlider && itemKey <= kItemBrightnessSlider) {
            // HSB滑块变化
            TriglavPlugInDouble h, s, br;
            propService->getDecimalValueProc(propertyObject, kItemHueSlider, &h);
            propService->getDecimalValueProc(propertyObject, kItemSaturationSlider, &s);
            propService->getDecimalValueProc(propertyObject, kItemBrightnessSlider, &br);

            HSBColor hsb;
            hsb.hue = static_cast<float>(h);
            hsb.saturation = static_cast<float>(s);
            hsb.brightness = static_cast<float>(br);

            TriglavPlugInRGBColor newColor = ColorSpaceConverter::HSBtoRGB(hsb);
            manager->SetCurrentColor(newColor);
        }
        else if (itemKey == kItemLuminanceLock) {
            TriglavPlugInBool locked;
            propService->getBooleanValueProc(propertyObject, kItemLuminanceLock, &locked);

            TriglavPlugInDouble luminance = 0.5;
            if (locked) {
                propService->getDecimalValueProc(propertyObject, kItemLuminanceValue, &luminance);
            }

            manager->GetConstraints().SetLuminanceLock(locked != 0, static_cast<float>(luminance));
        }
        else if (itemKey == kItemRYBMode) {
            TriglavPlugInBool rybMode;
            propService->getBooleanValueProc(propertyObject, kItemRYBMode, &rybMode);
            manager->GetConstraints().SetRYBMode(rybMode != 0);
        }
        else if (itemKey == kItemUndoColor) {
            manager->UndoColor();
        }
        else if (itemKey == kItemRedoColor) {
            manager->RedoColor();
        }
        else if (itemKey == kItemPaletteSortHue) {
            manager->GetPalette().SortByHue();
        }
        else if (itemKey == kItemPaletteSortBrightness) {
            manager->GetPalette().SortByBrightness();
        }
        else if (itemKey == kItemPaletteSortSaturation) {
            manager->GetPalette().SortBySaturation();
        }
        else if (itemKey == kItemPaletteClear) {
            manager->GetPalette().Clear();
        }
        else if (itemKey == kItemPresetPalette) {
            TriglavPlugInInt paletteIndex;
            propService2->getEnumerationValueProc(propertyObject, kItemPresetPalette, &paletteIndex);

            // 根据索引加载预设调色盘
            const char* paletteNames[] = {"Material", "Nature", "Warm", "Cool", "Monochrome",
                                         "Vintage", "Neon", "Pastel", "Autumn", "Spring"};

            if (paletteIndex >= 0 && paletteIndex < 10) {
                // 这里需要调用PresetPalettes的方法，但由于在回调中，我们简化处理
                manager->GetPalette().Clear();
                // 实际实现中应该调用PresetPalettes::GetPaletteByName(paletteNames[paletteIndex])
                // 然后将颜色添加到调色盘中
            }
        }
        else if (itemKey >= kItemPaletteSlot1 && itemKey <= kItemPaletteSlot8) {
            // 调色盘槽位点击 - 设置当前颜色
            TriglavPlugInRGBColor slotColor;
            propService->getRGBColorValueProc(propertyObject, itemKey, &slotColor);
            manager->SetCurrentColor(slotColor);
        }
        else if (itemKey >= kItemHistorySlot1 && itemKey <= kItemHistorySlot5) {
            // 历史槽位点击 - 设置当前颜色
            TriglavPlugInRGBColor historyColor;
            propService->getRGBColorValueProc(propertyObject, itemKey, &historyColor);
            manager->SetCurrentColor(historyColor);
        }

        // 检查预览状态
        TriglavPlugInBool previewEnabled;
        propService->getBooleanValueProc(propertyObject, kItemPreview, &previewEnabled);

        if (previewEnabled) {
            // 触发预览更新
            *result = kTriglavPlugInPropertyCallBackResultModify;
        }
    }
}

// 创建UI属性
void CreateColorManagerUI(TriglavPlugInPropertyObject propertyObject,
                         TriglavPlugInPropertyService* propertyService,
                         TriglavPlugInStringService* stringService,
                         TriglavPlugInHostObject hostObject,
                         TriglavPlugInServer* pluginServer) {
    
    // 颜色模式选择
    TriglavPlugInStringObject colorModeCaption = nullptr;
    stringService->createWithStringIDProc(&colorModeCaption, kStringIDColorMode, hostObject);
    propertyService->addItemProc(propertyObject, kItemColorMode,
        kTriglavPlugInPropertyValueTypeEnumeration, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, colorModeCaption, 'M');
    
    // 添加颜色模式选项
    TriglavPlugInStringObject rgbOption = nullptr;
    stringService->createWithAsciiStringProc(&rgbOption, "RGB", 3);
    pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_RGB, rgbOption, 'R');
    stringService->releaseProc(rgbOption);
    
    TriglavPlugInStringObject hsbOption = nullptr;
    stringService->createWithAsciiStringProc(&hsbOption, "HSB", 3);
    pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_HSB, hsbOption, 'H');
    stringService->releaseProc(hsbOption);

    TriglavPlugInStringObject cmykOption = nullptr;
    stringService->createWithAsciiStringProc(&cmykOption, "CMYK", 4);
    pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_CMYK, cmykOption, 'C');
    stringService->releaseProc(cmykOption);

    TriglavPlugInStringObject labOption = nullptr;
    stringService->createWithAsciiStringProc(&labOption, "Lab", 3);
    pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemColorMode, MODE_LAB, labOption, 'L');
    stringService->releaseProc(labOption);

    pluginServer->serviceSuite.propertyService2->setEnumerationValueProc(propertyObject, kItemColorMode, MODE_RGB);
    stringService->releaseProc(colorModeCaption);
    
    // 和谐类型选择
    TriglavPlugInStringObject harmonyCaption = nullptr;
    stringService->createWithStringIDProc(&harmonyCaption, kStringIDHarmonyType, hostObject);
    propertyService->addItemProc(propertyObject, kItemHarmonyType,
        kTriglavPlugInPropertyValueTypeEnumeration, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, harmonyCaption, 'H');
    
    // 添加和谐类型选项
    TriglavPlugInStringObject compOption = nullptr;
    stringService->createWithAsciiStringProc(&compOption, "Complementary", 13);
    pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemHarmonyType, HARMONY_COMPLEMENTARY, compOption, '1');
    stringService->releaseProc(compOption);

    TriglavPlugInStringObject anaOption = nullptr;
    stringService->createWithAsciiStringProc(&anaOption, "Analogous", 9);
    pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemHarmonyType, HARMONY_ANALOGOUS, anaOption, '2');
    stringService->releaseProc(anaOption);

    TriglavPlugInStringObject triOption = nullptr;
    stringService->createWithAsciiStringProc(&triOption, "Triadic", 7);
    pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemHarmonyType, HARMONY_TRIADIC, triOption, '3');
    stringService->releaseProc(triOption);

    pluginServer->serviceSuite.propertyService2->setEnumerationValueProc(propertyObject, kItemHarmonyType, HARMONY_COMPLEMENTARY);
    stringService->releaseProc(harmonyCaption);
    
    // RGB滑块
    TriglavPlugInStringObject redCaption = nullptr;
    stringService->createWithStringIDProc(&redCaption, kStringIDRGBRed, hostObject);
    propertyService->addItemProc(propertyObject, kItemRedSlider,
        kTriglavPlugInPropertyValueTypeInteger, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, redCaption, 'r');
    propertyService->setIntegerMinValueProc(propertyObject, kItemRedSlider, 0);
    propertyService->setIntegerMaxValueProc(propertyObject, kItemRedSlider, 255);
    propertyService->setIntegerValueProc(propertyObject, kItemRedSlider, 255);
    stringService->releaseProc(redCaption);
    
    TriglavPlugInStringObject greenCaption = nullptr;
    stringService->createWithStringIDProc(&greenCaption, kStringIDRGBGreen, hostObject);
    propertyService->addItemProc(propertyObject, kItemGreenSlider, 
        kTriglavPlugInPropertyValueTypeInteger, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, greenCaption, 'g');
    propertyService->setIntegerMinValueProc(propertyObject, kItemGreenSlider, 0);
    propertyService->setIntegerMaxValueProc(propertyObject, kItemGreenSlider, 255);
    propertyService->setIntegerValueProc(propertyObject, kItemGreenSlider, 255);
    stringService->releaseProc(greenCaption);
    
    TriglavPlugInStringObject blueCaption = nullptr;
    stringService->createWithStringIDProc(&blueCaption, kStringIDRGBBlue, hostObject);
    propertyService->addItemProc(propertyObject, kItemBlueSlider, 
        kTriglavPlugInPropertyValueTypeInteger, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, blueCaption, 'b');
    propertyService->setIntegerMinValueProc(propertyObject, kItemBlueSlider, 0);
    propertyService->setIntegerMaxValueProc(propertyObject, kItemBlueSlider, 255);
    propertyService->setIntegerValueProc(propertyObject, kItemBlueSlider, 255);
    stringService->releaseProc(blueCaption);

    // HSB滑块
    TriglavPlugInStringObject hueCaption = nullptr;
    stringService->createWithStringIDProc(&hueCaption, kStringIDHSBHue, hostObject);
    propertyService->addItemProc(propertyObject, kItemHueSlider,
        kTriglavPlugInPropertyValueTypeDecimal, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, hueCaption, 'h');
    propertyService->setDecimalMinValueProc(propertyObject, kItemHueSlider, 0.0);
    propertyService->setDecimalMaxValueProc(propertyObject, kItemHueSlider, 360.0);
    propertyService->setDecimalValueProc(propertyObject, kItemHueSlider, 0.0);
    stringService->releaseProc(hueCaption);

    TriglavPlugInStringObject saturationCaption = nullptr;
    stringService->createWithStringIDProc(&saturationCaption, kStringIDHSBSaturation, hostObject);
    propertyService->addItemProc(propertyObject, kItemSaturationSlider,
        kTriglavPlugInPropertyValueTypeDecimal, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, saturationCaption, 's');
    propertyService->setDecimalMinValueProc(propertyObject, kItemSaturationSlider, 0.0);
    propertyService->setDecimalMaxValueProc(propertyObject, kItemSaturationSlider, 1.0);
    propertyService->setDecimalValueProc(propertyObject, kItemSaturationSlider, 1.0);
    stringService->releaseProc(saturationCaption);

    TriglavPlugInStringObject brightnessCaption = nullptr;
    stringService->createWithStringIDProc(&brightnessCaption, kStringIDHSBBrightness, hostObject);
    propertyService->addItemProc(propertyObject, kItemBrightnessSlider,
        kTriglavPlugInPropertyValueTypeDecimal, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, brightnessCaption, 'b');
    propertyService->setDecimalMinValueProc(propertyObject, kItemBrightnessSlider, 0.0);
    propertyService->setDecimalMaxValueProc(propertyObject, kItemBrightnessSlider, 1.0);
    propertyService->setDecimalValueProc(propertyObject, kItemBrightnessSlider, 1.0);
    stringService->releaseProc(brightnessCaption);
    
    // 亮度锁定
    TriglavPlugInStringObject lumLockCaption = nullptr;
    stringService->createWithAsciiStringProc(&lumLockCaption, "Luminance Lock", 14);
    propertyService->addItemProc(propertyObject, kItemLuminanceLock, 
        kTriglavPlugInPropertyValueTypeBoolean, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, lumLockCaption, 'L');
    propertyService->setBooleanValueProc(propertyObject, kItemLuminanceLock, false);
    stringService->releaseProc(lumLockCaption);

    // 亮度锁定值滑块
    TriglavPlugInStringObject lumValueCaption = nullptr;
    stringService->createWithAsciiStringProc(&lumValueCaption, "Luminance Value", 15);
    propertyService->addItemProc(propertyObject, kItemLuminanceValue,
        kTriglavPlugInPropertyValueTypeDecimal, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, lumValueCaption, 'V');
    propertyService->setDecimalMinValueProc(propertyObject, kItemLuminanceValue, 0.0);
    propertyService->setDecimalMaxValueProc(propertyObject, kItemLuminanceValue, 1.0);
    propertyService->setDecimalValueProc(propertyObject, kItemLuminanceValue, 0.5);
    stringService->releaseProc(lumValueCaption);
    
    // RYB模式
    TriglavPlugInStringObject rybCaption = nullptr;
    stringService->createWithAsciiStringProc(&rybCaption, "RYB Mode", 8);
    propertyService->addItemProc(propertyObject, kItemRYBMode, 
        kTriglavPlugInPropertyValueTypeBoolean, kTriglavPlugInPropertyValueKindDefault, 
        kTriglavPlugInPropertyInputKindDefault, rybCaption, 'Y');
    propertyService->setBooleanValueProc(propertyObject, kItemRYBMode, false);
    stringService->releaseProc(rybCaption);
    
    // 历史记录控件
    TriglavPlugInStringObject undoCaption = nullptr;
    stringService->createWithStringIDProc(&undoCaption, kStringIDUndo, hostObject);
    propertyService->addItemProc(propertyObject, kItemUndoColor,
        kTriglavPlugInPropertyValueTypeVoid, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindPushButton, undoCaption, 'U');
    stringService->releaseProc(undoCaption);

    TriglavPlugInStringObject redoCaption = nullptr;
    stringService->createWithStringIDProc(&redoCaption, kStringIDRedo, hostObject);
    propertyService->addItemProc(propertyObject, kItemRedoColor,
        kTriglavPlugInPropertyValueTypeVoid, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindPushButton, redoCaption, 'R');
    stringService->releaseProc(redoCaption);

    // 调色盘操作按钮
    TriglavPlugInStringObject sortHueCaption = nullptr;
    stringService->createWithAsciiStringProc(&sortHueCaption, "Sort by Hue", 11);
    propertyService->addItemProc(propertyObject, kItemPaletteSortHue,
        kTriglavPlugInPropertyValueTypeVoid, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindPushButton, sortHueCaption, '1');
    stringService->releaseProc(sortHueCaption);

    TriglavPlugInStringObject sortBrightnessCaption = nullptr;
    stringService->createWithAsciiStringProc(&sortBrightnessCaption, "Sort by Brightness", 18);
    propertyService->addItemProc(propertyObject, kItemPaletteSortBrightness,
        kTriglavPlugInPropertyValueTypeVoid, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindPushButton, sortBrightnessCaption, '2');
    stringService->releaseProc(sortBrightnessCaption);

    TriglavPlugInStringObject sortSaturationCaption = nullptr;
    stringService->createWithAsciiStringProc(&sortSaturationCaption, "Sort by Saturation", 18);
    propertyService->addItemProc(propertyObject, kItemPaletteSortSaturation,
        kTriglavPlugInPropertyValueTypeVoid, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindPushButton, sortSaturationCaption, '3');
    stringService->releaseProc(sortSaturationCaption);

    TriglavPlugInStringObject clearPaletteCaption = nullptr;
    stringService->createWithAsciiStringProc(&clearPaletteCaption, "Clear Palette", 13);
    propertyService->addItemProc(propertyObject, kItemPaletteClear,
        kTriglavPlugInPropertyValueTypeVoid, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindPushButton, clearPaletteCaption, 'C');
    stringService->releaseProc(clearPaletteCaption);

    // 预设调色盘选择
    TriglavPlugInStringObject presetPaletteCaption = nullptr;
    stringService->createWithAsciiStringProc(&presetPaletteCaption, "Preset Palette", 14);
    propertyService->addItemProc(propertyObject, kItemPresetPalette,
        kTriglavPlugInPropertyValueTypeEnumeration, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, presetPaletteCaption, 'E');

    // 添加预设调色盘选项
    const char* paletteNames[] = {"Material", "Nature", "Warm", "Cool", "Monochrome",
                                 "Vintage", "Neon", "Pastel", "Autumn", "Spring"};

    for (int i = 0; i < 10; ++i) {
        TriglavPlugInStringObject paletteOption = nullptr;
        stringService->createWithAsciiStringProc(&paletteOption, paletteNames[i],
                                                 static_cast<TriglavPlugInInt>(strlen(paletteNames[i])));
        pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, kItemPresetPalette,
                                                                           i, paletteOption, '0' + i);
        stringService->releaseProc(paletteOption);
    }

    pluginServer->serviceSuite.propertyService2->setEnumerationValueProc(propertyObject, kItemPresetPalette, 0);
    stringService->releaseProc(presetPaletteCaption);

    // 颜色分析显示
    TriglavPlugInStringObject contrastCaption = nullptr;
    stringService->createWithAsciiStringProc(&contrastCaption, "Contrast Ratio", 14);
    propertyService->addItemProc(propertyObject, kItemContrastRatio,
        kTriglavPlugInPropertyValueTypeDecimal, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindHide, contrastCaption, 'T');
    propertyService->setDecimalValueProc(propertyObject, kItemContrastRatio, 1.0);
    stringService->releaseProc(contrastCaption);

    TriglavPlugInStringObject tempCaption = nullptr;
    stringService->createWithAsciiStringProc(&tempCaption, "Color Temperature", 17);
    propertyService->addItemProc(propertyObject, kItemColorTemperature,
        kTriglavPlugInPropertyValueTypeDecimal, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindHide, tempCaption, 'K');
    propertyService->setDecimalValueProc(propertyObject, kItemColorTemperature, 6500.0);
    stringService->releaseProc(tempCaption);

    // 调色盘槽位
    for (int i = 0; i < 8; ++i) {
        TriglavPlugInStringObject slotCaption = nullptr;
        char slotName[32];
        sprintf_s(slotName, sizeof(slotName), "Palette Slot %d", i + 1);
        stringService->createWithAsciiStringProc(&slotCaption, slotName, static_cast<TriglavPlugInInt>(strlen(slotName)));

        propertyService->addItemProc(propertyObject, kItemPaletteSlot1 + i,
            kTriglavPlugInPropertyValueTypeRGBColor, kTriglavPlugInPropertyValueKindDefault,
            kTriglavPlugInPropertyInputKindDefault, slotCaption, '1' + i);

        // 设置默认颜色为黑色
        TriglavPlugInRGBColor black = {0, 0, 0};
        propertyService->setRGBColorValueProc(propertyObject, kItemPaletteSlot1 + i, &black);

        stringService->releaseProc(slotCaption);
    }

    // 颜色历史槽位
    for (int i = 0; i < 5; ++i) {
        TriglavPlugInStringObject historyCaption = nullptr;
        char historyName[32];
        sprintf_s(historyName, sizeof(historyName), "History %d", i + 1);
        stringService->createWithAsciiStringProc(&historyCaption, historyName, static_cast<TriglavPlugInInt>(strlen(historyName)));

        propertyService->addItemProc(propertyObject, kItemHistorySlot1 + i,
            kTriglavPlugInPropertyValueTypeRGBColor, kTriglavPlugInPropertyValueKindDefault,
            kTriglavPlugInPropertyInputKindHide, historyCaption, 'A' + i);

        // 设置默认颜色为白色
        TriglavPlugInRGBColor white = {255, 255, 255};
        propertyService->setRGBColorValueProc(propertyObject, kItemHistorySlot1 + i, &white);

        stringService->releaseProc(historyCaption);
    }

    // 预览控件
    TriglavPlugInStringObject previewCaption = nullptr;
    stringService->createWithStringIDProc(&previewCaption, kStringIDPreview, hostObject);
    propertyService->addItemProc(propertyObject, kItemPreview,
        kTriglavPlugInPropertyValueTypeBoolean, kTriglavPlugInPropertyValueKindDefault,
        kTriglavPlugInPropertyInputKindDefault, previewCaption, 'P');
    propertyService->setBooleanValueProc(propertyObject, kItemPreview, true);
    stringService->releaseProc(previewCaption);
}

// 主插件入口函数
void TRIGLAV_PLUGIN_API TriglavPluginCall(
    TriglavPlugInInt* result,
    TriglavPlugInPtr* data,
    TriglavPlugInInt selector,
    TriglavPlugInServer* pluginServer,
    TriglavPlugInPtr reserved) {

    *result = kTriglavPlugInCallResultFailed;

    if (pluginServer == nullptr) return;

    if (selector == kTriglavPlugInSelectorModuleInitialize) {
        // 模块初始化
        *data = new ColorManagerInfo();
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        info->manager = new ColorManager();
        info->propertyService = nullptr;
        info->propertyService2 = nullptr;
        info->stringService = nullptr;
        info->offscreenService = nullptr;
        info->preview = true;

        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorModuleTerminate) {
        // 模块终止
        if (*data != nullptr) {
            ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
            if (info->manager != nullptr) {
                delete info->manager;
            }
            delete info;
            *data = nullptr;
        }
        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorFilterInitialize) {
        // 滤镜初始化
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        if (info == nullptr || info->manager == nullptr) return;

        TriglavPlugInRecordSuite* recordSuite = &pluginServer->recordSuite;
        TriglavPlugInPropertyService* propertyService = pluginServer->serviceSuite.propertyService;
        TriglavPlugInStringService* stringService = pluginServer->serviceSuite.stringService;
        TriglavPlugInHostObject hostObject = pluginServer->hostObject;

        if (propertyService == nullptr || stringService == nullptr) return;

        info->propertyService = propertyService;
        info->propertyService2 = pluginServer->serviceSuite.propertyService2;
        info->stringService = stringService;
        info->offscreenService = pluginServer->serviceSuite.offscreenService;

        // 创建属性对象
        TriglavPlugInPropertyObject propertyObject = nullptr;
        propertyService->createProc(&propertyObject);

        if (propertyObject == nullptr) return;

        // 创建UI
        CreateColorManagerUI(propertyObject, propertyService, stringService, hostObject, pluginServer);

        // 初始化颜色管理器
        info->manager->Initialize(propertyService, propertyObject);

        // 设置属性和回调
        recordSuite->filterInitializeRecord->setPropertyProc(recordSuite, hostObject, propertyObject);
        recordSuite->filterInitializeRecord->setPropertyCallBackProc(recordSuite, hostObject,
            ColorManagerPropertyCallBack, *data);

        // 释放属性对象
        propertyService->releaseProc(propertyObject);

        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorFilterTerminate) {
        // 滤镜终止
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        if (info != nullptr && info->manager != nullptr) {
            info->manager->Shutdown();
        }
        *result = kTriglavPlugInCallResultSuccess;
    }
    else if (selector == kTriglavPlugInSelectorFilterRun) {
        // 滤镜运行
        ColorManagerInfo* info = static_cast<ColorManagerInfo*>(*data);
        if (info == nullptr || info->manager == nullptr) return;

        TriglavPlugInRecordSuite* recordSuite = &pluginServer->recordSuite;
        TriglavPlugInHostObject hostObject = pluginServer->hostObject;

        if (recordSuite->filterRunRecord == nullptr) return;

        // 主循环
        while (true) {
            TriglavPlugInInt processResult;
            recordSuite->filterRunRecord->processProc(recordSuite, hostObject,
                kTriglavPlugInFilterRunProcessStateEnd, &processResult);

            if (processResult == kTriglavPlugInFilterRunProcessResultRestart) {
                // 重新开始
                continue;
            }
            else if (processResult == kTriglavPlugInFilterRunProcessResultExit) {
                // 退出
                break;
            }
        }

        *result = kTriglavPlugInCallResultSuccess;
    }
}
