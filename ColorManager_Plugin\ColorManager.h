#pragma once

#include "TriglavPlugInSDK/TriglavPlugInSDK.h"
#include <vector>
#include <cmath>

// 颜色结构定义
struct HSBColor {
    float hue;        // 色相 (0-360)
    float saturation; // 饱和度 (0-1)
    float brightness; // 亮度 (0-1)
};

struct LabColor {
    float L;  // 亮度 (0-100)
    float a;  // 红绿轴 (-128 to 127)
    float b;  // 黄蓝轴 (-128 to 127)
};

struct RYBColor {
    float r, y, b;  // Red, Yellow, Blue (0-1)
};

// 颜色模式枚举
enum ColorMode {
    MODE_RGB = 0,
    MODE_CMYK = 1,
    MODE_HSB = 2,
    MODE_LAB = 3,
    MODE_GRAYSCALE = 4,
    MODE_BITMAP = 5
};

// 颜色和谐类型
enum HarmonyType {
    HARMONY_COMPLEMENTARY = 0,    // 互补色
    HARMONY_ANALOGOUS = 1,        // 邻近色
    HARMONY_TRIADIC = 2,          // 三角色
    HARMONY_SPLIT_COMPLEMENTARY = 3, // 分割互补色
    HARMONY_TETRADIC = 4          // 四角色
};

// 颜色空间转换器
class ColorSpaceConverter {
public:
    // RGB 转换
    static HSBColor RGBtoHSB(const TriglavPlugInRGBColor& rgb);
    static TriglavPlugInRGBColor HSBtoRGB(const HSBColor& hsb);
    
    static LabColor RGBtoLab(const TriglavPlugInRGBColor& rgb);
    static TriglavPlugInRGBColor LabtoRGB(const LabColor& lab);
    
    static TriglavPlugInCMYKColor RGBtoCMYK(const TriglavPlugInRGBColor& rgb);
    static TriglavPlugInRGBColor CMYKtoRGB(const TriglavPlugInCMYKColor& cmyk);
    
    static RYBColor RGBtoRYB(const TriglavPlugInRGBColor& rgb);
    static TriglavPlugInRGBColor RYBtoRGB(const RYBColor& ryb);
    
    // 灰度转换
    static TriglavPlugInUInt8 RGBtoGrayscale(const TriglavPlugInRGBColor& rgb);
    static TriglavPlugInRGBColor GrayscaletoRGB(TriglavPlugInUInt8 gray);
    
    // 位图转换
    static bool RGBtoBitmap(const TriglavPlugInRGBColor& rgb, float threshold = 0.5f);
    static TriglavPlugInRGBColor BitmaptoRGB(bool bit);

private:
    // 辅助函数
    static float RGBtoXYZ_Helper(float value);
    static float XYZtoRGB_Helper(float value);
    static float LabtoXYZ_Helper(float value);
    static float XYZtoLab_Helper(float value);
};

// 颜色和谐生成器
class ColorHarmony {
public:
    static std::vector<TriglavPlugInRGBColor> GenerateHarmony(
        const TriglavPlugInRGBColor& baseColor, 
        HarmonyType type
    );
    
    static std::vector<TriglavPlugInRGBColor> GetComplementary(const TriglavPlugInRGBColor& base);
    static std::vector<TriglavPlugInRGBColor> GetAnalogous(const TriglavPlugInRGBColor& base);
    static std::vector<TriglavPlugInRGBColor> GetTriadic(const TriglavPlugInRGBColor& base);
    static std::vector<TriglavPlugInRGBColor> GetSplitComplementary(const TriglavPlugInRGBColor& base);
    static std::vector<TriglavPlugInRGBColor> GetTetradic(const TriglavPlugInRGBColor& base);

private:
    static TriglavPlugInRGBColor RotateHue(const TriglavPlugInRGBColor& color, float degrees);
};

// 颜色约束管理器
class ColorConstraints {
private:
    bool luminanceLocked;
    float lockedLuminance;
    
    bool gamutLocked;
    TriglavPlugInRGBColor gamutMin;
    TriglavPlugInRGBColor gamutMax;
    float saturationLimit;
    
    bool useRYBMode;

public:
    ColorConstraints();
    
    // 亮度锁定
    void SetLuminanceLock(bool enabled, float luminance = 0.5f);
    TriglavPlugInRGBColor ApplyLuminanceLock(const TriglavPlugInRGBColor& color);
    
    // 色域锁定
    void SetGamutLock(bool enabled, const TriglavPlugInRGBColor& minColor, 
                      const TriglavPlugInRGBColor& maxColor, float satLimit = 1.0f);
    TriglavPlugInRGBColor ApplyGamutLock(const TriglavPlugInRGBColor& color);
    
    // RGB/RYB模式
    void SetRYBMode(bool enabled) { useRYBMode = enabled; }
    bool IsRYBMode() const { return useRYBMode; }
    
    // 应用所有约束
    TriglavPlugInRGBColor ApplyAllConstraints(const TriglavPlugInRGBColor& color);
};

// 颜色历史记录管理器
class ColorHistory {
private:
    std::vector<TriglavPlugInRGBColor> history;
    size_t maxHistorySize;
    int currentIndex;

public:
    ColorHistory(size_t maxSize = 50);

    void AddColor(const TriglavPlugInRGBColor& color);
    bool CanUndo() const;
    bool CanRedo() const;
    TriglavPlugInRGBColor Undo();
    TriglavPlugInRGBColor Redo();
    TriglavPlugInRGBColor GetCurrentColor() const;

    void Clear();
    size_t GetHistoryCount() const { return history.size(); }
    TriglavPlugInRGBColor GetHistoryColor(size_t index) const;
};

// 调色盘管理器
class ColorPalette {
private:
    std::vector<TriglavPlugInRGBColor> colors;
    size_t maxColors;

public:
    ColorPalette(size_t maxSize = 16);

    void AddColor(const TriglavPlugInRGBColor& color);
    void RemoveColor(size_t index);
    void SetColor(size_t index, const TriglavPlugInRGBColor& color);
    TriglavPlugInRGBColor GetColor(size_t index) const;

    size_t GetColorCount() const { return colors.size(); }
    size_t GetMaxColors() const { return maxColors; }

    void Clear();
    void GenerateHarmonyPalette(const TriglavPlugInRGBColor& baseColor, HarmonyType type);

    // 新增功能
    void SortByHue();
    void SortByBrightness();
    void SortBySaturation();
    std::vector<TriglavPlugInRGBColor> ExportPalette() const;
    void ImportPalette(const std::vector<TriglavPlugInRGBColor>& palette);
};

// 颜色采样器
class ColorSampler {
public:
    static TriglavPlugInRGBColor SampleFromPoint(
        TriglavPlugInOffscreenObject offscreen,
        TriglavPlugInOffscreenService* offscreenService,
        const TriglavPlugInPoint& point
    );

    static std::vector<TriglavPlugInRGBColor> SampleFromArea(
        TriglavPlugInOffscreenObject offscreen,
        TriglavPlugInOffscreenService* offscreenService,
        const TriglavPlugInRect& area,
        int sampleCount = 10
    );
};

// 颜色分析工具
class ColorAnalyzer {
public:
    // 对比度分析 (WCAG标准)
    static float CalculateContrast(const TriglavPlugInRGBColor& color1, const TriglavPlugInRGBColor& color2);
    static bool IsAccessible(const TriglavPlugInRGBColor& foreground, const TriglavPlugInRGBColor& background, bool largeText = false);

    // 颜色盲友好性测试
    static TriglavPlugInRGBColor SimulateProtanopia(const TriglavPlugInRGBColor& color);  // 红色盲
    static TriglavPlugInRGBColor SimulateDeuteranopia(const TriglavPlugInRGBColor& color); // 绿色盲
    static TriglavPlugInRGBColor SimulateTritanopia(const TriglavPlugInRGBColor& color);   // 蓝色盲

    // 颜色温度分析
    static float CalculateColorTemperature(const TriglavPlugInRGBColor& color);
    static bool IsWarmColor(const TriglavPlugInRGBColor& color);
    static bool IsCoolColor(const TriglavPlugInRGBColor& color);

private:
    static float GetRelativeLuminance(const TriglavPlugInRGBColor& color);
};

// 主要颜色管理器类
class ColorManager {
private:
    ColorSpaceConverter converter;
    ColorHarmony harmony;
    ColorConstraints constraints;
    ColorPalette palette;
    ColorHistory history;
    ColorSampler sampler;
    ColorAnalyzer analyzer;

    // 当前状态
    TriglavPlugInRGBColor currentColor;
    ColorMode currentMode;
    HarmonyType currentHarmonyType;

    // CSP插件相关
    TriglavPlugInPropertyService* propertyService;
    TriglavPlugInPropertyObject propertyObject;

public:
    ColorManager();
    
    // 初始化和清理
    void Initialize(TriglavPlugInPropertyService* propService, 
                   TriglavPlugInPropertyObject propObject);
    void Shutdown();
    
    // 颜色设置和获取
    void SetCurrentColor(const TriglavPlugInRGBColor& color);
    TriglavPlugInRGBColor GetCurrentColor() const { return currentColor; }
    
    // 模式切换
    void SetColorMode(ColorMode mode);
    ColorMode GetColorMode() const { return currentMode; }
    
    // 和谐色生成
    void SetHarmonyType(HarmonyType type);
    std::vector<TriglavPlugInRGBColor> GetHarmonyColors();
    
    // 约束管理
    ColorConstraints& GetConstraints() { return constraints; }
    
    // 调色盘管理
    ColorPalette& GetPalette() { return palette; }

    // 历史记录管理
    ColorHistory& GetHistory() { return history; }
    void UndoColor();
    void RedoColor();

    // 颜色采样
    void SampleColorFromCanvas(const TriglavPlugInPoint& point);
    void SampleColorsFromArea(const TriglavPlugInRect& area);

    // 颜色分析
    ColorAnalyzer& GetAnalyzer() { return analyzer; }
    float GetCurrentColorContrast(const TriglavPlugInRGBColor& backgroundColor);
    bool IsCurrentColorAccessible(const TriglavPlugInRGBColor& backgroundColor);

    // UI更新
    void UpdateUI();
    void HandlePropertyChange(TriglavPlugInInt itemKey);

    // 颜色转换接口
    HSBColor GetCurrentColorAsHSB();
    LabColor GetCurrentColorAsLab();
    TriglavPlugInCMYKColor GetCurrentColorAsCMYK();
    TriglavPlugInUInt8 GetCurrentColorAsGrayscale();
    bool GetCurrentColorAsBitmap();
};

// 渐变相关枚举和类定义
enum GradientType {
    GRADIENT_LINEAR = 0,
    GRADIENT_RADIAL = 1,
    GRADIENT_ANGULAR = 2,
    GRADIENT_DIAMOND = 3
};

enum InterpolationMode {
    INTERPOLATION_RGB = 0,
    INTERPOLATION_HSB = 1,
    INTERPOLATION_LAB = 2
};

struct GradientStop {
    float position;  // 0.0 到 1.0
    TriglavPlugInRGBColor color;

    GradientStop(float pos, const TriglavPlugInRGBColor& col)
        : position(pos), color(col) {}
};

class ColorGradient {
private:
    std::vector<GradientStop> stops;
    GradientType gradientType;
    InterpolationMode interpolationMode;

public:
    ColorGradient();

    void AddStop(float position, const TriglavPlugInRGBColor& color);
    void ClearStops();
    void SetGradientType(GradientType type);
    void SetInterpolationMode(InterpolationMode mode);

    TriglavPlugInRGBColor GetColorAt(float position);
    std::vector<TriglavPlugInRGBColor> GeneratePalette(int colorCount);
    std::vector<TriglavPlugInRGBColor> Generate2DGradient(int width, int height,
                                                         float centerX = 0.5f, float centerY = 0.5f);

private:
    TriglavPlugInRGBColor InterpolateColors(const TriglavPlugInRGBColor& color1,
                                           const TriglavPlugInRGBColor& color2,
                                           float t);
    TriglavPlugInRGBColor InterpolateRGB(const TriglavPlugInRGBColor& color1,
                                        const TriglavPlugInRGBColor& color2,
                                        float t);
    TriglavPlugInRGBColor InterpolateHSB(const TriglavPlugInRGBColor& color1,
                                        const TriglavPlugInRGBColor& color2,
                                        float t);
    TriglavPlugInRGBColor InterpolateLab(const TriglavPlugInRGBColor& color1,
                                        const TriglavPlugInRGBColor& color2,
                                        float t);
    float Calculate2DPosition(int x, int y, int width, int height, float centerX, float centerY);
};

class PresetGradients {
public:
    static ColorGradient CreateRainbow();
    static ColorGradient CreateSunset();
    static ColorGradient CreateOcean();
    static ColorGradient CreateFire();
};
