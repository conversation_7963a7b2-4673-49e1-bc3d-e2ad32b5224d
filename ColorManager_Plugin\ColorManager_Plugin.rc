#include "resource.h"
#include <windows.h>

// 版本信息
VS_VERSION_INFO VERSIONINFO
FILEVERSION 1,2,0,0
PRODUCTVERSION 1,2,0,0
FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
FILEFLAGS 0x1L
#else
FILEFLAGS 0x0L
#endif
FILEOS 0x40004L
FILETYPE 0x2L
FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "CSP Color Manager Plugin"
            VALUE "FileDescription", "Advanced Color Management Plugin for Clip Studio Paint"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "ColorManager_Plugin"
            VALUE "LegalCopyright", "Copyright (C) 2024"
            VALUE "OriginalFilename", "ColorManager_Plugin.dll"
            VALUE "ProductName", "CSP Advanced Color Manager"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

// 字符串资源
STRINGTABLE
BEGIN
    101 "Color Management"
    102 "Advanced Color Manager"
    103 "Color Mode"
    104 "Harmony Type"
    105 "Red"
    106 "Green"
    107 "Blue"
    108 "Hue"
    109 "Saturation"
    110 "Brightness"
    111 "Preview"
    112 "Undo"
    113 "Redo"
    114 "Color History"
    115 "Color Analysis"
    116 "Sample Color"
    117 "Palette Operations"
END

// 图标资源 (如果有的话)
// IDI_ICON1 ICON "icon.ico"

// 对话框资源 (如果需要的话)
/*
IDD_ABOUT DIALOGEX 0, 0, 200, 100
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "About Advanced Color Manager"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,143,79,50,14
    LTEXT           "Advanced Color Manager v1.2",IDC_STATIC,7,7,186,8
    LTEXT           "Professional color management plugin for Clip Studio Paint",IDC_STATIC,7,20,186,8
    LTEXT           "Features:",IDC_STATIC,7,35,186,8
    LTEXT           "• Multiple color space support (RGB, CMYK, HSB, Lab)",IDC_STATIC,15,45,178,8
    LTEXT           "• Color harmony generation",IDC_STATIC,15,55,178,8
    LTEXT           "• Advanced color constraints",IDC_STATIC,15,65,178,8
END
*/
