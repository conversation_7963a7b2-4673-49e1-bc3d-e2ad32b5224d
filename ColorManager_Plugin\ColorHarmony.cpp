#include "ColorManager.h"
#include <cmath>

// 生成颜色和谐方案
std::vector<TriglavPlugInRGBColor> ColorHarmony::GenerateHarmony(
    const TriglavPlugInRGBColor& baseColor, 
    HarmonyType type) {
    
    switch (type) {
        case HARMONY_COMPLEMENTARY:
            return GetComplementary(baseColor);
        case HARMONY_ANALOGOUS:
            return GetAnalogous(baseColor);
        case HARMONY_TRIADIC:
            return GetTriadic(baseColor);
        case HARMONY_SPLIT_COMPLEMENTARY:
            return GetSplitComplementary(baseColor);
        case HARMONY_TETRADIC:
            return GetTetradic(baseColor);
        default:
            return {baseColor};
    }
}

// 互补色 (180度对面)
std::vector<TriglavPlugInRGBColor> ColorHarmony::GetComplementary(const TriglavPlugInRGBColor& base) {
    std::vector<TriglavPlugInRGBColor> result;
    result.push_back(base);
    result.push_back(RotateHue(base, 180.0f));
    return result;
}

// 邻近色 (相邻30度)
std::vector<TriglavPlugInRGBColor> ColorHarmony::GetAnalogous(const TriglavPlugInRGBColor& base) {
    std::vector<TriglavPlugInRGBColor> result;
    result.push_back(RotateHue(base, -30.0f));
    result.push_back(base);
    result.push_back(RotateHue(base, 30.0f));
    return result;
}

// 三角色 (120度间隔)
std::vector<TriglavPlugInRGBColor> ColorHarmony::GetTriadic(const TriglavPlugInRGBColor& base) {
    std::vector<TriglavPlugInRGBColor> result;
    result.push_back(base);
    result.push_back(RotateHue(base, 120.0f));
    result.push_back(RotateHue(base, 240.0f));
    return result;
}

// 分割互补色 (150度和210度)
std::vector<TriglavPlugInRGBColor> ColorHarmony::GetSplitComplementary(const TriglavPlugInRGBColor& base) {
    std::vector<TriglavPlugInRGBColor> result;
    result.push_back(base);
    result.push_back(RotateHue(base, 150.0f));
    result.push_back(RotateHue(base, 210.0f));
    return result;
}

// 四角色 (90度间隔)
std::vector<TriglavPlugInRGBColor> ColorHarmony::GetTetradic(const TriglavPlugInRGBColor& base) {
    std::vector<TriglavPlugInRGBColor> result;
    result.push_back(base);
    result.push_back(RotateHue(base, 90.0f));
    result.push_back(RotateHue(base, 180.0f));
    result.push_back(RotateHue(base, 270.0f));
    return result;
}

// 色相旋转辅助函数
TriglavPlugInRGBColor ColorHarmony::RotateHue(const TriglavPlugInRGBColor& color, float degrees) {
    // 转换到HSB
    HSBColor hsb = ColorSpaceConverter::RGBtoHSB(color);
    
    // 旋转色相
    hsb.hue += degrees;
    while (hsb.hue >= 360.0f) hsb.hue -= 360.0f;
    while (hsb.hue < 0.0f) hsb.hue += 360.0f;
    
    // 转换回RGB
    return ColorSpaceConverter::HSBtoRGB(hsb);
}

// 颜色约束管理器实现
ColorConstraints::ColorConstraints() 
    : luminanceLocked(false), lockedLuminance(0.5f)
    , gamutLocked(false), saturationLimit(1.0f)
    , useRYBMode(false) {
    
    // 默认色域范围
    gamutMin.red = gamutMin.green = gamutMin.blue = 0;
    gamutMax.red = gamutMax.green = gamutMax.blue = 255;
}

// 设置亮度锁定
void ColorConstraints::SetLuminanceLock(bool enabled, float luminance) {
    luminanceLocked = enabled;
    lockedLuminance = std::max(0.0f, std::min(1.0f, luminance));
}

// 应用亮度锁定
TriglavPlugInRGBColor ColorConstraints::ApplyLuminanceLock(const TriglavPlugInRGBColor& color) {
    if (!luminanceLocked) return color;
    
    // 转换到Lab空间进行亮度调整
    LabColor lab = ColorSpaceConverter::RGBtoLab(color);
    lab.L = lockedLuminance * 100.0f;  // L值范围是0-100
    
    return ColorSpaceConverter::LabtoRGB(lab);
}

// 设置色域锁定
void ColorConstraints::SetGamutLock(bool enabled, const TriglavPlugInRGBColor& minColor, 
                                   const TriglavPlugInRGBColor& maxColor, float satLimit) {
    gamutLocked = enabled;
    gamutMin = minColor;
    gamutMax = maxColor;
    saturationLimit = std::max(0.0f, std::min(1.0f, satLimit));
}

// 应用色域锁定
TriglavPlugInRGBColor ColorConstraints::ApplyGamutLock(const TriglavPlugInRGBColor& color) {
    if (!gamutLocked) return color;
    
    TriglavPlugInRGBColor result = color;
    
    // 限制RGB值范围
    result.red = std::max(gamutMin.red, std::min(gamutMax.red, result.red));
    result.green = std::max(gamutMin.green, std::min(gamutMax.green, result.green));
    result.blue = std::max(gamutMin.blue, std::min(gamutMax.blue, result.blue));
    
    // 限制饱和度
    if (saturationLimit < 1.0f) {
        HSBColor hsb = ColorSpaceConverter::RGBtoHSB(result);
        hsb.saturation = std::min(hsb.saturation, saturationLimit);
        result = ColorSpaceConverter::HSBtoRGB(hsb);
    }
    
    return result;
}

// 应用所有约束
TriglavPlugInRGBColor ColorConstraints::ApplyAllConstraints(const TriglavPlugInRGBColor& color) {
    TriglavPlugInRGBColor result = color;
    
    // 按顺序应用约束
    result = ApplyGamutLock(result);
    result = ApplyLuminanceLock(result);
    
    return result;
}

// 调色盘管理器实现
ColorPalette::ColorPalette(size_t maxSize) : maxColors(maxSize) {
    colors.reserve(maxSize);
}

void ColorPalette::AddColor(const TriglavPlugInRGBColor& color) {
    if (colors.size() < maxColors) {
        colors.push_back(color);
    }
}

void ColorPalette::RemoveColor(size_t index) {
    if (index < colors.size()) {
        colors.erase(colors.begin() + index);
    }
}

void ColorPalette::SetColor(size_t index, const TriglavPlugInRGBColor& color) {
    if (index < colors.size()) {
        colors[index] = color;
    } else if (index == colors.size() && colors.size() < maxColors) {
        colors.push_back(color);
    }
}

TriglavPlugInRGBColor ColorPalette::GetColor(size_t index) const {
    if (index < colors.size()) {
        return colors[index];
    }
    // 返回黑色作为默认值
    TriglavPlugInRGBColor black = {0, 0, 0};
    return black;
}

void ColorPalette::Clear() {
    colors.clear();
}

void ColorPalette::GenerateHarmonyPalette(const TriglavPlugInRGBColor& baseColor, HarmonyType type) {
    Clear();
    std::vector<TriglavPlugInRGBColor> harmonyColors = ColorHarmony::GenerateHarmony(baseColor, type);

    for (const auto& color : harmonyColors) {
        AddColor(color);
    }

    // 如果还有空间，添加一些变化
    if (colors.size() < maxColors) {
        HSBColor baseHSB = ColorSpaceConverter::RGBtoHSB(baseColor);

        // 添加不同亮度的变化
        for (int i = 1; i <= 3 && colors.size() < maxColors; ++i) {
            HSBColor variant = baseHSB;
            variant.brightness = std::max(0.1f, std::min(1.0f, baseHSB.brightness + (i * 0.2f - 0.3f)));
            AddColor(ColorSpaceConverter::HSBtoRGB(variant));
        }

        // 添加不同饱和度的变化
        for (int i = 1; i <= 3 && colors.size() < maxColors; ++i) {
            HSBColor variant = baseHSB;
            variant.saturation = std::max(0.1f, std::min(1.0f, baseHSB.saturation + (i * 0.2f - 0.3f)));
            AddColor(ColorSpaceConverter::HSBtoRGB(variant));
        }
    }
}

// 调色盘排序功能
void ColorPalette::SortByHue() {
    std::sort(colors.begin(), colors.end(), [](const TriglavPlugInRGBColor& a, const TriglavPlugInRGBColor& b) {
        HSBColor hsbA = ColorSpaceConverter::RGBtoHSB(a);
        HSBColor hsbB = ColorSpaceConverter::RGBtoHSB(b);
        return hsbA.hue < hsbB.hue;
    });
}

void ColorPalette::SortByBrightness() {
    std::sort(colors.begin(), colors.end(), [](const TriglavPlugInRGBColor& a, const TriglavPlugInRGBColor& b) {
        HSBColor hsbA = ColorSpaceConverter::RGBtoHSB(a);
        HSBColor hsbB = ColorSpaceConverter::RGBtoHSB(b);
        return hsbA.brightness < hsbB.brightness;
    });
}

void ColorPalette::SortBySaturation() {
    std::sort(colors.begin(), colors.end(), [](const TriglavPlugInRGBColor& a, const TriglavPlugInRGBColor& b) {
        HSBColor hsbA = ColorSpaceConverter::RGBtoHSB(a);
        HSBColor hsbB = ColorSpaceConverter::RGBtoHSB(b);
        return hsbA.saturation < hsbB.saturation;
    });
}

std::vector<TriglavPlugInRGBColor> ColorPalette::ExportPalette() const {
    return colors;
}

void ColorPalette::ImportPalette(const std::vector<TriglavPlugInRGBColor>& palette) {
    Clear();
    for (const auto& color : palette) {
        if (colors.size() >= maxColors) break;
        AddColor(color);
    }
}

// 主要颜色管理器实现
ColorManager::ColorManager() 
    : currentMode(MODE_RGB)
    , currentHarmonyType(HARMONY_COMPLEMENTARY)
    , propertyService(nullptr)
    , propertyObject(nullptr) {
    
    // 初始化为白色
    currentColor.red = currentColor.green = currentColor.blue = 255;
}

void ColorManager::Initialize(TriglavPlugInPropertyService* propService, 
                             TriglavPlugInPropertyObject propObject) {
    propertyService = propService;
    propertyObject = propObject;
}

void ColorManager::Shutdown() {
    propertyService = nullptr;
    propertyObject = nullptr;
}

void ColorManager::SetCurrentColor(const TriglavPlugInRGBColor& color) {
    currentColor = constraints.ApplyAllConstraints(color);
    UpdateUI();
}

void ColorManager::SetColorMode(ColorMode mode) {
    currentMode = mode;
    UpdateUI();
}

void ColorManager::SetHarmonyType(HarmonyType type) {
    currentHarmonyType = type;
    palette.GenerateHarmonyPalette(currentColor, type);
    UpdateUI();
}

std::vector<TriglavPlugInRGBColor> ColorManager::GetHarmonyColors() {
    return harmony.GenerateHarmony(currentColor, currentHarmonyType);
}

// 颜色转换接口
HSBColor ColorManager::GetCurrentColorAsHSB() {
    return converter.RGBtoHSB(currentColor);
}

LabColor ColorManager::GetCurrentColorAsLab() {
    return converter.RGBtoLab(currentColor);
}

TriglavPlugInCMYKColor ColorManager::GetCurrentColorAsCMYK() {
    return converter.RGBtoCMYK(currentColor);
}

TriglavPlugInUInt8 ColorManager::GetCurrentColorAsGrayscale() {
    return converter.RGBtoGrayscale(currentColor);
}

bool ColorManager::GetCurrentColorAsBitmap() {
    return converter.RGBtoBitmap(currentColor);
}

void ColorManager::UpdateUI() {
    // UI更新逻辑将在主文件中实现
}

// ColorManager的新增方法实现
void ColorManager::UndoColor() {
    if (history.CanUndo()) {
        TriglavPlugInRGBColor previousColor = history.Undo();
        currentColor = previousColor;
        UpdateUI();
    }
}

void ColorManager::RedoColor() {
    if (history.CanRedo()) {
        TriglavPlugInRGBColor nextColor = history.Redo();
        currentColor = nextColor;
        UpdateUI();
    }
}

void ColorManager::SampleColorFromCanvas(const TriglavPlugInPoint& point) {
    // 这个方法需要在主文件中实现，因为需要访问CSP的离屏对象
}

void ColorManager::SampleColorsFromArea(const TriglavPlugInRect& area) {
    // 这个方法需要在主文件中实现，因为需要访问CSP的离屏对象
}

float ColorManager::GetCurrentColorContrast(const TriglavPlugInRGBColor& backgroundColor) {
    return analyzer.CalculateContrast(currentColor, backgroundColor);
}

bool ColorManager::IsCurrentColorAccessible(const TriglavPlugInRGBColor& backgroundColor) {
    return analyzer.IsAccessible(currentColor, backgroundColor);
}

void ColorManager::HandlePropertyChange(TriglavPlugInInt itemKey) {
    // 属性变化处理逻辑将在主文件中实现
}
