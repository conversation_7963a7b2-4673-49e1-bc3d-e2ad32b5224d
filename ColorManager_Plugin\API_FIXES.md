# CSP SDK API 修正说明

根据提供的CSP SDK API文档，对插件代码进行了以下重要修正：

## 🔧 主要修正内容

### 1. **记录套件函数调用修正**

**修正前 (错误):**
```cpp
TriglavPlugInFilterInitializeSetProperty(recordSuite, hostObject, propertyObject);
TriglavPlugInFilterInitializeSetPropertyCallBack(recordSuite, hostObject, callback, data);
```

**修正后 (正确):**
```cpp
recordSuite->filterInitializeRecord->setPropertyProc(recordSuite, hostObject, propertyObject);
recordSuite->filterInitializeRecord->setPropertyCallBackProc(recordSuite, hostObject, callback, data);
```

### 2. **属性服务参数顺序修正**

**修正前 (错误):**
```cpp
propService->getIntegerValueProc(&value, propertyObject, itemKey);
propService->setIntegerValueProc(propertyObject, itemKey, value);
```

**修正后 (正确):**
```cpp
propService->getIntegerValueProc(propertyObject, itemKey, &value);
propService->setIntegerValueProc(propertyObject, itemKey, value);
```

### 3. **服务套件访问修正**

**修正前 (错误):**
```cpp
propertyService->addEnumerationItemProc(propertyObject, itemKey, value, string, key);
```

**修正后 (正确):**
```cpp
pluginServer->serviceSuite.propertyService2->addEnumerationItemProc(propertyObject, itemKey, value, string, key);
```

### 4. **离屏服务API修正**

**修正前 (错误):**
```cpp
offscreenService->getBlockImageProc(&address, &rowBytes, &pixelBytes, &blockRect, offscreen, &point);
```

**修正后 (正确):**
```cpp
offscreenService->getBlockImageProc(offscreen, &point, &address, &rowBytes, &pixelBytes, &blockRect);
```

### 5. **插件数据结构增强**

**修正前:**
```cpp
struct ColorManagerInfo {
    ColorManager* manager;
    TriglavPlugInPropertyService* propertyService;
    bool preview;
};
```

**修正后:**
```cpp
struct ColorManagerInfo {
    ColorManager* manager;
    TriglavPlugInPropertyService* propertyService;
    TriglavPlugInPropertyService2* propertyService2;
    TriglavPlugInStringService* stringService;
    TriglavPlugInOffscreenService* offscreenService;
    bool preview;
};
```

## 📋 API使用规范总结

### 记录套件 (Record Suite)
- 通过 `recordSuite->filterInitializeRecord->` 访问初始化函数
- 通过 `recordSuite->filterRunRecord->` 访问运行时函数
- 参数顺序：`(recordSuite, hostObject, ...)`

### 属性服务 (Property Service)
- 基础属性操作使用 `propertyService`
- 枚举操作使用 `propertyService2`
- Get函数参数顺序：`(propertyObject, itemKey, &value)`
- Set函数参数顺序：`(propertyObject, itemKey, value)`

### 服务套件访问
- 通过 `pluginServer->serviceSuite.serviceName` 访问各种服务
- 主要服务：
  - `propertyService` - 基础属性操作
  - `propertyService2` - 高级属性操作
  - `stringService` - 字符串操作
  - `offscreenService` - 离屏图像操作

### 离屏服务 (Offscreen Service)
- 参数顺序：`(offscreen, point, &address, &rowBytes, &pixelBytes, &blockRect)`
- 用于图像数据访问和像素操作

## ⚠️ 常见错误避免

1. **不要直接调用全局函数** - 所有API都通过服务套件访问
2. **注意参数顺序** - Get/Set函数的参数顺序不同
3. **正确的服务选择** - 基础操作用service，高级操作用service2
4. **错误检查** - 始终检查函数返回值和空指针

## 🔍 验证清单

- [x] 记录套件函数调用方式正确
- [x] 属性服务参数顺序正确
- [x] 服务套件访问路径正确
- [x] 离屏服务API使用正确
- [x] 插件数据结构包含必要的服务指针
- [x] 错误检查和空指针验证

## 📝 后续建议

1. **添加更多错误检查** - 对所有API调用添加返回值检查
2. **优化内存管理** - 确保所有创建的对象都正确释放
3. **完善回调处理** - 在属性回调中添加更完整的错误处理
4. **测试验证** - 在实际CSP环境中测试所有功能

这些修正确保了插件代码符合CSP SDK的API规范，应该能够正确编译和运行。
