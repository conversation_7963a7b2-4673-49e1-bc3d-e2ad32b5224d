#include "ColorManager.h"

// 预设调色盘类
class PresetPalettes {
public:
    // 材质调色盘
    static std::vector<TriglavPlugInRGBColor> GetMaterialPalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        // 木材色调
        palette.push_back({139, 69, 19});   // 深棕色
        palette.push_back({160, 82, 45});   // 马鞍棕
        palette.push_back({210, 180, 140}); // 棕褐色
        palette.push_back({222, 184, 135}); // 浅棕色
        
        // 金属色调
        palette.push_back({192, 192, 192}); // 银色
        palette.push_back({255, 215, 0});   // 金色
        palette.push_back({184, 134, 11});  // 深金色
        palette.push_back({105, 105, 105}); // 暗灰色
        
        return palette;
    }
    
    // 自然调色盘
    static std::vector<TriglavPlugInRGBColor> GetNaturePalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        // 天空和水
        palette.push_back({135, 206, 235}); // 天蓝色
        palette.push_back({0, 191, 255});   // 深天蓝
        palette.push_back({25, 25, 112});   // 午夜蓝
        
        // 植物
        palette.push_back({34, 139, 34});   // 森林绿
        palette.push_back({107, 142, 35});  // 橄榄绿
        palette.push_back({154, 205, 50});  // 黄绿色
        
        // 大地
        palette.push_back({160, 82, 45});   // 马鞍棕
        palette.push_back({210, 180, 140}); // 棕褐色
        
        return palette;
    }
    
    // 暖色调色盘
    static std::vector<TriglavPlugInRGBColor> GetWarmPalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        palette.push_back({255, 69, 0});    // 红橙色
        palette.push_back({255, 140, 0});   // 深橙色
        palette.push_back({255, 165, 0});   // 橙色
        palette.push_back({255, 215, 0});   // 金色
        palette.push_back({255, 255, 0});   // 黄色
        palette.push_back({255, 20, 147});  // 深粉红
        palette.push_back({255, 105, 180}); // 热粉红
        palette.push_back({255, 182, 193}); // 浅粉红
        
        return palette;
    }
    
    // 冷色调色盘
    static std::vector<TriglavPlugInRGBColor> GetCoolPalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        palette.push_back({0, 0, 139});     // 深蓝色
        palette.push_back({0, 0, 255});     // 蓝色
        palette.push_back({30, 144, 255});  // 道奇蓝
        palette.push_back({135, 206, 235}); // 天蓝色
        palette.push_back({0, 255, 255});   // 青色
        palette.push_back({0, 139, 139});   // 深青色
        palette.push_back({0, 128, 128});   // 蓝绿色
        palette.push_back({128, 0, 128});   // 紫色
        
        return palette;
    }
    
    // 单色调色盘 (灰度)
    static std::vector<TriglavPlugInRGBColor> GetMonochromePalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        for (int i = 0; i < 8; ++i) {
            TriglavPlugInUInt8 value = static_cast<TriglavPlugInUInt8>(i * 255 / 7);
            palette.push_back({value, value, value});
        }
        
        return palette;
    }
    
    // 复古调色盘
    static std::vector<TriglavPlugInRGBColor> GetVintagePalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        palette.push_back({139, 69, 19});   // 深棕色
        palette.push_back({160, 82, 45});   // 马鞍棕
        palette.push_back({205, 133, 63});  // 秘鲁色
        palette.push_back({222, 184, 135}); // 浅棕色
        palette.push_back({240, 230, 140}); // 卡其色
        palette.push_back({189, 183, 107}); // 深卡其色
        palette.push_back({128, 128, 0});   // 橄榄色
        palette.push_back({85, 107, 47});   // 深橄榄绿
        
        return palette;
    }
    
    // 霓虹调色盘
    static std::vector<TriglavPlugInRGBColor> GetNeonPalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        palette.push_back({255, 20, 147});  // 深粉红
        palette.push_back({255, 0, 255});   // 品红
        palette.push_back({138, 43, 226});  // 蓝紫色
        palette.push_back({75, 0, 130});    // 靛青
        palette.push_back({0, 255, 255});   // 青色
        palette.push_back({50, 205, 50});   // 酸橙绿
        palette.push_back({255, 255, 0});   // 黄色
        palette.push_back({255, 69, 0});    // 红橙色
        
        return palette;
    }
    
    // 柔和调色盘
    static std::vector<TriglavPlugInRGBColor> GetPastelPalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        palette.push_back({255, 182, 193}); // 浅粉红
        palette.push_back({255, 160, 122}); // 浅鲑鱼色
        palette.push_back({255, 218, 185}); // 桃色
        palette.push_back({255, 255, 224}); // 浅黄色
        palette.push_back({240, 255, 240}); // 蜜瓜色
        palette.push_back({230, 230, 250}); // 薰衣草色
        palette.push_back({221, 160, 221}); // 梅花色
        palette.push_back({175, 238, 238}); // 浅青色
        
        return palette;
    }
    
    // 秋季调色盘
    static std::vector<TriglavPlugInRGBColor> GetAutumnPalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        palette.push_back({139, 69, 19});   // 深棕色
        palette.push_back({160, 82, 45});   // 马鞍棕
        palette.push_back({205, 92, 92});   // 印度红
        palette.push_back({255, 69, 0});    // 红橙色
        palette.push_back({255, 140, 0});   // 深橙色
        palette.push_back({255, 215, 0});   // 金色
        palette.push_back({184, 134, 11});  // 深金色
        palette.push_back({128, 128, 0});   // 橄榄色
        
        return palette;
    }
    
    // 春季调色盘
    static std::vector<TriglavPlugInRGBColor> GetSpringPalette() {
        std::vector<TriglavPlugInRGBColor> palette;
        
        palette.push_back({255, 192, 203}); // 粉红色
        palette.push_back({255, 105, 180}); // 热粉红
        palette.push_back({255, 20, 147});  // 深粉红
        palette.push_back({50, 205, 50});   // 酸橙绿
        palette.push_back({0, 255, 127});   // 春绿色
        palette.push_back({152, 251, 152}); // 浅绿色
        palette.push_back({255, 255, 0});   // 黄色
        palette.push_back({255, 215, 0});   // 金色
        
        return palette;
    }
    
    // 获取所有预设调色盘的名称
    static std::vector<std::string> GetPaletteNames() {
        return {
            "Material",
            "Nature", 
            "Warm",
            "Cool",
            "Monochrome",
            "Vintage",
            "Neon",
            "Pastel",
            "Autumn",
            "Spring"
        };
    }
    
    // 根据名称获取调色盘
    static std::vector<TriglavPlugInRGBColor> GetPaletteByName(const std::string& name) {
        if (name == "Material") return GetMaterialPalette();
        if (name == "Nature") return GetNaturePalette();
        if (name == "Warm") return GetWarmPalette();
        if (name == "Cool") return GetCoolPalette();
        if (name == "Monochrome") return GetMonochromePalette();
        if (name == "Vintage") return GetVintagePalette();
        if (name == "Neon") return GetNeonPalette();
        if (name == "Pastel") return GetPastelPalette();
        if (name == "Autumn") return GetAutumnPalette();
        if (name == "Spring") return GetSpringPalette();
        
        // 默认返回材质调色盘
        return GetMaterialPalette();
    }
};
