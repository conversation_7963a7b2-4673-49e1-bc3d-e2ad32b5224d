#include "ColorManager.h"
#include <algorithm>
#include <cmath>

// RGB to HSB 转换
HSBColor ColorSpaceConverter::RGBtoHSB(const TriglavPlugInRGBColor& rgb) {
    float r = rgb.red / 255.0f;
    float g = rgb.green / 255.0f;
    float b = rgb.blue / 255.0f;
    
    float max_val = std::max({r, g, b});
    float min_val = std::min({r, g, b});
    float delta = max_val - min_val;
    
    HSBColor hsb;
    
    // 计算色相 (Hue)
    if (delta == 0) {
        hsb.hue = 0;
    } else if (max_val == r) {
        hsb.hue = 60.0f * fmod(((g - b) / delta), 6.0f);
    } else if (max_val == g) {
        hsb.hue = 60.0f * (((b - r) / delta) + 2.0f);
    } else {
        hsb.hue = 60.0f * (((r - g) / delta) + 4.0f);
    }
    
    if (hsb.hue < 0) hsb.hue += 360.0f;
    
    // 计算饱和度 (Saturation)
    hsb.saturation = (max_val == 0) ? 0 : (delta / max_val);
    
    // 计算亮度 (Brightness)
    hsb.brightness = max_val;
    
    return hsb;
}

// HSB to RGB 转换
TriglavPlugInRGBColor ColorSpaceConverter::HSBtoRGB(const HSBColor& hsb) {
    float c = hsb.brightness * hsb.saturation;
    float x = c * (1.0f - fabs(fmod(hsb.hue / 60.0f, 2.0f) - 1.0f));
    float m = hsb.brightness - c;
    
    float r, g, b;
    
    if (hsb.hue >= 0 && hsb.hue < 60) {
        r = c; g = x; b = 0;
    } else if (hsb.hue >= 60 && hsb.hue < 120) {
        r = x; g = c; b = 0;
    } else if (hsb.hue >= 120 && hsb.hue < 180) {
        r = 0; g = c; b = x;
    } else if (hsb.hue >= 180 && hsb.hue < 240) {
        r = 0; g = x; b = c;
    } else if (hsb.hue >= 240 && hsb.hue < 300) {
        r = x; g = 0; b = c;
    } else {
        r = c; g = 0; b = x;
    }
    
    TriglavPlugInRGBColor rgb;
    rgb.red = static_cast<TriglavPlugInUInt8>((r + m) * 255);
    rgb.green = static_cast<TriglavPlugInUInt8>((g + m) * 255);
    rgb.blue = static_cast<TriglavPlugInUInt8>((b + m) * 255);
    
    return rgb;
}

// RGB to Lab 转换
LabColor ColorSpaceConverter::RGBtoLab(const TriglavPlugInRGBColor& rgb) {
    // 首先转换到XYZ色彩空间
    float r = RGBtoXYZ_Helper(rgb.red / 255.0f);
    float g = RGBtoXYZ_Helper(rgb.green / 255.0f);
    float b = RGBtoXYZ_Helper(rgb.blue / 255.0f);
    
    // RGB to XYZ (sRGB色彩空间)
    float x = r * 0.4124564f + g * 0.3575761f + b * 0.1804375f;
    float y = r * 0.2126729f + g * 0.7151522f + b * 0.0721750f;
    float z = r * 0.0193339f + g * 0.1191920f + b * 0.9503041f;
    
    // 标准化到D65白点
    x /= 0.95047f;
    y /= 1.00000f;
    z /= 1.08883f;
    
    // XYZ to Lab
    x = XYZtoLab_Helper(x);
    y = XYZtoLab_Helper(y);
    z = XYZtoLab_Helper(z);
    
    LabColor lab;
    lab.L = (116.0f * y) - 16.0f;
    lab.a = 500.0f * (x - y);
    lab.b = 200.0f * (y - z);
    
    return lab;
}

// Lab to RGB 转换
TriglavPlugInRGBColor ColorSpaceConverter::LabtoRGB(const LabColor& lab) {
    float y = (lab.L + 16.0f) / 116.0f;
    float x = lab.a / 500.0f + y;
    float z = y - lab.b / 200.0f;
    
    x = LabtoXYZ_Helper(x) * 0.95047f;
    y = LabtoXYZ_Helper(y) * 1.00000f;
    z = LabtoXYZ_Helper(z) * 1.08883f;
    
    // XYZ to RGB
    float r = x * 3.2404542f + y * -1.5371385f + z * -0.4985314f;
    float g = x * -0.9692660f + y * 1.8760108f + z * 0.0415560f;
    float b = x * 0.0556434f + y * -0.2040259f + z * 1.0572252f;
    
    r = XYZtoRGB_Helper(r);
    g = XYZtoRGB_Helper(g);
    b = XYZtoRGB_Helper(b);
    
    TriglavPlugInRGBColor rgb;
    rgb.red = static_cast<TriglavPlugInUInt8>(std::max(0.0f, std::min(1.0f, r)) * 255);
    rgb.green = static_cast<TriglavPlugInUInt8>(std::max(0.0f, std::min(1.0f, g)) * 255);
    rgb.blue = static_cast<TriglavPlugInUInt8>(std::max(0.0f, std::min(1.0f, b)) * 255);
    
    return rgb;
}

// RGB to CMYK 转换
TriglavPlugInCMYKColor ColorSpaceConverter::RGBtoCMYK(const TriglavPlugInRGBColor& rgb) {
    float r = rgb.red / 255.0f;
    float g = rgb.green / 255.0f;
    float b = rgb.blue / 255.0f;
    
    float k = 1.0f - std::max({r, g, b});
    
    TriglavPlugInCMYKColor cmyk;
    if (k == 1.0f) {
        cmyk.cyan = 0;
        cmyk.magenta = 0;
        cmyk.yellow = 0;
    } else {
        cmyk.cyan = static_cast<TriglavPlugInUInt8>((1.0f - r - k) / (1.0f - k) * 255);
        cmyk.magenta = static_cast<TriglavPlugInUInt8>((1.0f - g - k) / (1.0f - k) * 255);
        cmyk.yellow = static_cast<TriglavPlugInUInt8>((1.0f - b - k) / (1.0f - k) * 255);
    }
    cmyk.keyplate = static_cast<TriglavPlugInUInt8>(k * 255);
    
    return cmyk;
}

// CMYK to RGB 转换
TriglavPlugInRGBColor ColorSpaceConverter::CMYKtoRGB(const TriglavPlugInCMYKColor& cmyk) {
    float c = cmyk.cyan / 255.0f;
    float m = cmyk.magenta / 255.0f;
    float y = cmyk.yellow / 255.0f;
    float k = cmyk.keyplate / 255.0f;
    
    TriglavPlugInRGBColor rgb;
    rgb.red = static_cast<TriglavPlugInUInt8>((1.0f - c) * (1.0f - k) * 255);
    rgb.green = static_cast<TriglavPlugInUInt8>((1.0f - m) * (1.0f - k) * 255);
    rgb.blue = static_cast<TriglavPlugInUInt8>((1.0f - y) * (1.0f - k) * 255);
    
    return rgb;
}

// RGB to RYB 转换 (艺术家色轮)
RYBColor ColorSpaceConverter::RGBtoRYB(const TriglavPlugInRGBColor& rgb) {
    float r = rgb.red / 255.0f;
    float g = rgb.green / 255.0f;
    float b = rgb.blue / 255.0f;
    
    // 移除白色成分
    float w = std::min({r, g, b});
    r -= w; g -= w; b -= w;
    
    float mg = std::max({r, g, b});
    
    // 获取黄色成分
    float y = std::min(r, g);
    r -= y; g -= y;
    
    // 如果这个过程产生了蓝色和绿色，那么将它们转换为蓝色
    if (b > 0 && g > 0) {
        b += g;
        g = 0;
    }
    
    // 重新分配红色到黄色
    if (b > 0 && r > 0) {
        if (b > r) {
            b -= r;
            r = 0;
        } else {
            r -= b;
            b = 0;
        }
    }
    
    // 标准化
    float my = std::max({r, y, b});
    if (my > 0) {
        float n = mg / my;
        r *= n; y *= n; b *= n;
    }
    
    // 添加回白色成分
    r += w; y += w; b += w;
    
    RYBColor ryb;
    ryb.r = r;
    ryb.y = y;
    ryb.b = b;
    
    return ryb;
}

// RYB to RGB 转换
TriglavPlugInRGBColor ColorSpaceConverter::RYBtoRGB(const RYBColor& ryb) {
    float r = ryb.r;
    float y = ryb.y;
    float b = ryb.b;
    
    // 移除白色成分
    float w = std::min({r, y, b});
    r -= w; y -= w; b -= w;
    
    float my = std::max({r, y, b});
    
    // 获取绿色成分
    float g = std::min(y, b);
    y -= g; b -= g;
    
    if (b > 0 && r > 0) {
        if (b > r) {
            b -= r;
            r = 0;
        } else {
            r -= b;
            b = 0;
        }
    }
    
    if (r > 0 && g > 0) {
        r += g;
        g = 0;
    }
    
    // 标准化
    float mg = std::max({r, y + g, b});
    if (mg > 0) {
        float n = my / mg;
        r *= n; g *= n; b *= n; y *= n;
    }
    
    // 添加回白色成分
    r += w; g += w + y; b += w;
    
    TriglavPlugInRGBColor rgb;
    rgb.red = static_cast<TriglavPlugInUInt8>(std::max(0.0f, std::min(1.0f, r)) * 255);
    rgb.green = static_cast<TriglavPlugInUInt8>(std::max(0.0f, std::min(1.0f, g)) * 255);
    rgb.blue = static_cast<TriglavPlugInUInt8>(std::max(0.0f, std::min(1.0f, b)) * 255);
    
    return rgb;
}

// 灰度转换
TriglavPlugInUInt8 ColorSpaceConverter::RGBtoGrayscale(const TriglavPlugInRGBColor& rgb) {
    // 使用标准的亮度权重
    return static_cast<TriglavPlugInUInt8>(
        0.299f * rgb.red + 0.587f * rgb.green + 0.114f * rgb.blue
    );
}

TriglavPlugInRGBColor ColorSpaceConverter::GrayscaletoRGB(TriglavPlugInUInt8 gray) {
    TriglavPlugInRGBColor rgb;
    rgb.red = rgb.green = rgb.blue = gray;
    return rgb;
}

// 位图转换
bool ColorSpaceConverter::RGBtoBitmap(const TriglavPlugInRGBColor& rgb, float threshold) {
    TriglavPlugInUInt8 gray = RGBtoGrayscale(rgb);
    return (gray / 255.0f) > threshold;
}

TriglavPlugInRGBColor ColorSpaceConverter::BitmaptoRGB(bool bit) {
    TriglavPlugInUInt8 value = bit ? 255 : 0;
    return GrayscaletoRGB(value);
}

// 辅助函数实现
float ColorSpaceConverter::RGBtoXYZ_Helper(float value) {
    if (value > 0.04045f) {
        return pow((value + 0.055f) / 1.055f, 2.4f);
    } else {
        return value / 12.92f;
    }
}

float ColorSpaceConverter::XYZtoRGB_Helper(float value) {
    if (value > 0.0031308f) {
        return 1.055f * pow(value, 1.0f / 2.4f) - 0.055f;
    } else {
        return 12.92f * value;
    }
}

float ColorSpaceConverter::LabtoXYZ_Helper(float value) {
    if (value > 0.206893f) {
        return pow(value, 3.0f);
    } else {
        return (value - 16.0f / 116.0f) / 7.787f;
    }
}

float ColorSpaceConverter::XYZtoLab_Helper(float value) {
    if (value > 0.008856f) {
        return pow(value, 1.0f / 3.0f);
    } else {
        return (7.787f * value) + (16.0f / 116.0f);
    }
}
