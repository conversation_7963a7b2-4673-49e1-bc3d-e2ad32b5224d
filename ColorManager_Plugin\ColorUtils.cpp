#include "ColorManager.h"
#include <algorithm>
#include <cmath>

// 颜色工具类实现
class ColorUtils {
public:
    // 颜色混合
    static TriglavPlugInRGBColor BlendColors(const TriglavPlugInRGBColor& color1, 
                                            const TriglavPlugInRGBColor& color2, 
                                            float ratio) {
        ratio = std::max(0.0f, std::min(1.0f, ratio));
        
        TriglavPlugInRGBColor result;
        result.red = static_cast<TriglavPlugInUInt8>(color1.red * (1.0f - ratio) + color2.red * ratio);
        result.green = static_cast<TriglavPlugInUInt8>(color1.green * (1.0f - ratio) + color2.green * ratio);
        result.blue = static_cast<TriglavPlugInUInt8>(color1.blue * (1.0f - ratio) + color2.blue * ratio);
        
        return result;
    }
    
    // 颜色变亮
    static TriglavPlugInRGBColor LightenColor(const TriglavPlugInRGBColor& color, float amount) {
        TriglavPlugInRGBColor white = {255, 255, 255};
        return BlendColors(color, white, amount);
    }
    
    // 颜色变暗
    static TriglavPlugInRGBColor DarkenColor(const TriglavPlugInRGBColor& color, float amount) {
        TriglavPlugInRGBColor black = {0, 0, 0};
        return BlendColors(color, black, amount);
    }
    
    // 增加饱和度
    static TriglavPlugInRGBColor SaturateColor(const TriglavPlugInRGBColor& color, float amount) {
        HSBColor hsb = ColorSpaceConverter::RGBtoHSB(color);
        hsb.saturation = std::max(0.0f, std::min(1.0f, hsb.saturation + amount));
        return ColorSpaceConverter::HSBtoRGB(hsb);
    }
    
    // 降低饱和度
    static TriglavPlugInRGBColor DesaturateColor(const TriglavPlugInRGBColor& color, float amount) {
        return SaturateColor(color, -amount);
    }
    
    // 调整色相
    static TriglavPlugInRGBColor ShiftHue(const TriglavPlugInRGBColor& color, float degrees) {
        HSBColor hsb = ColorSpaceConverter::RGBtoHSB(color);
        hsb.hue += degrees;
        while (hsb.hue >= 360.0f) hsb.hue -= 360.0f;
        while (hsb.hue < 0.0f) hsb.hue += 360.0f;
        return ColorSpaceConverter::HSBtoRGB(hsb);
    }
    
    // 反转颜色
    static TriglavPlugInRGBColor InvertColor(const TriglavPlugInRGBColor& color) {
        TriglavPlugInRGBColor result;
        result.red = 255 - color.red;
        result.green = 255 - color.green;
        result.blue = 255 - color.blue;
        return result;
    }
    
    // 获取颜色的平均值
    static TriglavPlugInRGBColor AverageColors(const std::vector<TriglavPlugInRGBColor>& colors) {
        if (colors.empty()) {
            TriglavPlugInRGBColor black = {0, 0, 0};
            return black;
        }
        
        long totalR = 0, totalG = 0, totalB = 0;
        for (const auto& color : colors) {
            totalR += color.red;
            totalG += color.green;
            totalB += color.blue;
        }
        
        TriglavPlugInRGBColor result;
        result.red = static_cast<TriglavPlugInUInt8>(totalR / colors.size());
        result.green = static_cast<TriglavPlugInUInt8>(totalG / colors.size());
        result.blue = static_cast<TriglavPlugInUInt8>(totalB / colors.size());
        
        return result;
    }
    
    // 颜色距离计算 (欧几里得距离)
    static float ColorDistance(const TriglavPlugInRGBColor& color1, const TriglavPlugInRGBColor& color2) {
        float dr = static_cast<float>(color1.red - color2.red);
        float dg = static_cast<float>(color1.green - color2.green);
        float db = static_cast<float>(color1.blue - color2.blue);
        
        return sqrt(dr * dr + dg * dg + db * db);
    }
    
    // 查找最相似的颜色
    static size_t FindClosestColor(const TriglavPlugInRGBColor& targetColor, 
                                  const std::vector<TriglavPlugInRGBColor>& palette) {
        if (palette.empty()) return 0;
        
        size_t closestIndex = 0;
        float minDistance = ColorDistance(targetColor, palette[0]);
        
        for (size_t i = 1; i < palette.size(); ++i) {
            float distance = ColorDistance(targetColor, palette[i]);
            if (distance < minDistance) {
                minDistance = distance;
                closestIndex = i;
            }
        }
        
        return closestIndex;
    }
    
    // 生成单色调色盘 (不同亮度的同一色相)
    static std::vector<TriglavPlugInRGBColor> GenerateMonochromaticPalette(
        const TriglavPlugInRGBColor& baseColor, int count) {
        
        std::vector<TriglavPlugInRGBColor> palette;
        if (count <= 0) return palette;
        
        HSBColor baseHSB = ColorSpaceConverter::RGBtoHSB(baseColor);
        
        for (int i = 0; i < count; ++i) {
            HSBColor variant = baseHSB;
            variant.brightness = static_cast<float>(i + 1) / count;
            palette.push_back(ColorSpaceConverter::HSBtoRGB(variant));
        }
        
        return palette;
    }
    
    // 生成类似色调色盘 (相似色相)
    static std::vector<TriglavPlugInRGBColor> GenerateAnalogousPalette(
        const TriglavPlugInRGBColor& baseColor, int count, float hueRange = 60.0f) {
        
        std::vector<TriglavPlugInRGBColor> palette;
        if (count <= 0) return palette;
        
        HSBColor baseHSB = ColorSpaceConverter::RGBtoHSB(baseColor);
        
        for (int i = 0; i < count; ++i) {
            HSBColor variant = baseHSB;
            float offset = (static_cast<float>(i) / (count - 1) - 0.5f) * hueRange;
            variant.hue += offset;
            
            while (variant.hue >= 360.0f) variant.hue -= 360.0f;
            while (variant.hue < 0.0f) variant.hue += 360.0f;
            
            palette.push_back(ColorSpaceConverter::HSBtoRGB(variant));
        }
        
        return palette;
    }
    
    // 颜色字符串转换 (十六进制)
    static std::string ColorToHex(const TriglavPlugInRGBColor& color) {
        char hexString[8];
        sprintf_s(hexString, sizeof(hexString), "#%02X%02X%02X", color.red, color.green, color.blue);
        return std::string(hexString);
    }
    
    // 十六进制字符串转颜色
    static TriglavPlugInRGBColor HexToColor(const std::string& hexString) {
        TriglavPlugInRGBColor color = {0, 0, 0};
        
        if (hexString.length() >= 7 && hexString[0] == '#') {
            unsigned int r, g, b;
            if (sscanf_s(hexString.c_str() + 1, "%02X%02X%02X", &r, &g, &b) == 3) {
                color.red = static_cast<TriglavPlugInUInt8>(r);
                color.green = static_cast<TriglavPlugInUInt8>(g);
                color.blue = static_cast<TriglavPlugInUInt8>(b);
            }
        }
        
        return color;
    }
    
    // 判断颜色是否为深色
    static bool IsDarkColor(const TriglavPlugInRGBColor& color) {
        // 使用感知亮度公式
        float luminance = 0.299f * color.red + 0.587f * color.green + 0.114f * color.blue;
        return luminance < 128.0f;
    }
    
    // 获取适合的文本颜色 (黑色或白色)
    static TriglavPlugInRGBColor GetContrastTextColor(const TriglavPlugInRGBColor& backgroundColor) {
        if (IsDarkColor(backgroundColor)) {
            TriglavPlugInRGBColor white = {255, 255, 255};
            return white;
        } else {
            TriglavPlugInRGBColor black = {0, 0, 0};
            return black;
        }
    }
    
    // 颜色量化 (减少颜色数量)
    static TriglavPlugInRGBColor QuantizeColor(const TriglavPlugInRGBColor& color, int levels) {
        if (levels <= 1) levels = 2;
        if (levels > 256) levels = 256;
        
        float step = 255.0f / (levels - 1);
        
        TriglavPlugInRGBColor result;
        result.red = static_cast<TriglavPlugInUInt8>(round(color.red / step) * step);
        result.green = static_cast<TriglavPlugInUInt8>(round(color.green / step) * step);
        result.blue = static_cast<TriglavPlugInUInt8>(round(color.blue / step) * step);
        
        return result;
    }
};
