//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by ColorManager_Plugin.rc
//

#define IDS_FILTER_CATEGORY_NAME        101
#define IDS_FILTER_NAME                 102
#define IDS_COLOR_MODE                  103
#define IDS_HARMONY_TYPE                104
#define IDS_RGB_RED                     105
#define IDS_RGB_GREEN                   106
#define IDS_RGB_BLUE                    107
#define IDS_HSB_HUE                     108
#define IDS_HSB_SATURATION              109
#define IDS_HSB_BRIGHTNESS              110
#define IDS_PREVIEW                     111
#define IDS_UNDO                        112
#define IDS_REDO                        113
#define IDS_COLOR_HISTORY               114
#define IDS_COLOR_ANALYSIS              115
#define IDS_SAMPLE_COLOR                116
#define IDS_PALETTE_OPERATIONS          117

// 对话框资源ID
#define IDD_ABOUT                       1000

// 图标资源ID
#define IDI_ICON1                       2000

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        3000
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1001
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
