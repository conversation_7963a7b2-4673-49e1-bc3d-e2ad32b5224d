#include "ColorManager.h"
#include <cmath>
#include <algorithm>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// ColorGradient 实现
ColorGradient::ColorGradient() : gradientType(GRADIENT_LINEAR), interpolationMode(INTERPOLATION_RGB) {}
    
void ColorGradient::AddStop(float position, const TriglavPlugInRGBColor& color) {
        position = std::max(0.0f, std::min(1.0f, position));
        stops.emplace_back(position, color);
        
        // 按位置排序
        std::sort(stops.begin(), stops.end(), 
            [](const GradientStop& a, const GradientStop& b) {
                return a.position < b.position;
            });
}

void ColorGradient::ClearStops() {
    stops.clear();
}

void ColorGradient::SetGradientType(GradientType type) {
    gradientType = type;
}

void ColorGradient::SetInterpolationMode(InterpolationMode mode) {
    interpolationMode = mode;
}
TriglavPlugInRGBColor ColorGradient::GetColorAt(float position) {
        if (stops.empty()) {
            TriglavPlugInRGBColor black = {0, 0, 0};
            return black;
        }
        
        if (stops.size() == 1) {
            return stops[0].color;
        }
        
        position = std::max(0.0f, std::min(1.0f, position));
        
        // 找到相邻的两个停止点
        size_t leftIndex = 0;
        size_t rightIndex = stops.size() - 1;
        
        for (size_t i = 0; i < stops.size() - 1; ++i) {
            if (position >= stops[i].position && position <= stops[i + 1].position) {
                leftIndex = i;
                rightIndex = i + 1;
                break;
            }
        }
        
        if (leftIndex == rightIndex) {
            return stops[leftIndex].color;
        }
        
        // 计算插值因子
        float leftPos = stops[leftIndex].position;
        float rightPos = stops[rightIndex].position;
        float t = (position - leftPos) / (rightPos - leftPos);
        
    // 根据插值模式进行颜色插值
    return InterpolateColors(stops[leftIndex].color, stops[rightIndex].color, t);
}

std::vector<TriglavPlugInRGBColor> ColorGradient::GeneratePalette(int colorCount) {
        std::vector<TriglavPlugInRGBColor> palette;
        
        if (colorCount <= 0) return palette;
        
        for (int i = 0; i < colorCount; ++i) {
            float position = static_cast<float>(i) / (colorCount - 1);
            palette.push_back(GetColorAt(position));
        }
        
    return palette;
}

std::vector<TriglavPlugInRGBColor> ColorGradient::Generate2DGradient(int width, int height,
                                                     float centerX, float centerY) {
        std::vector<TriglavPlugInRGBColor> imageData(width * height);
        
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                float position = Calculate2DPosition(x, y, width, height, centerX, centerY);
                imageData[y * width + x] = GetColorAt(position);
            }
        }
        
    return imageData;
}

TriglavPlugInRGBColor ColorGradient::InterpolateColors(const TriglavPlugInRGBColor& color1,
                                       const TriglavPlugInRGBColor& color2,
                                       float t) {
        switch (interpolationMode) {
            case INTERPOLATION_RGB:
                return InterpolateRGB(color1, color2, t);
            case INTERPOLATION_HSB:
                return InterpolateHSB(color1, color2, t);
            case INTERPOLATION_LAB:
                return InterpolateLab(color1, color2, t);
            default:
            return InterpolateRGB(color1, color2, t);
    }
}

TriglavPlugInRGBColor ColorGradient::InterpolateRGB(const TriglavPlugInRGBColor& color1,
                                    const TriglavPlugInRGBColor& color2,
                                    float t) {
        TriglavPlugInRGBColor result;
        result.red = static_cast<TriglavPlugInUInt8>(color1.red + t * (color2.red - color1.red));
        result.green = static_cast<TriglavPlugInUInt8>(color1.green + t * (color2.green - color1.green));
        result.blue = static_cast<TriglavPlugInUInt8>(color1.blue + t * (color2.blue - color1.blue));
    return result;
}

TriglavPlugInRGBColor ColorGradient::InterpolateHSB(const TriglavPlugInRGBColor& color1,
                                    const TriglavPlugInRGBColor& color2,
                                    float t) {
        HSBColor hsb1 = ColorSpaceConverter::RGBtoHSB(color1);
        HSBColor hsb2 = ColorSpaceConverter::RGBtoHSB(color2);
        
        // 处理色相的循环性质
        float hueDiff = hsb2.hue - hsb1.hue;
        if (hueDiff > 180.0f) {
            hueDiff -= 360.0f;
        } else if (hueDiff < -180.0f) {
            hueDiff += 360.0f;
        }
        
        HSBColor result;
        result.hue = hsb1.hue + t * hueDiff;
        if (result.hue < 0.0f) result.hue += 360.0f;
        if (result.hue >= 360.0f) result.hue -= 360.0f;
        
        result.saturation = hsb1.saturation + t * (hsb2.saturation - hsb1.saturation);
        result.brightness = hsb1.brightness + t * (hsb2.brightness - hsb1.brightness);
        
    return ColorSpaceConverter::HSBtoRGB(result);
}

TriglavPlugInRGBColor ColorGradient::InterpolateLab(const TriglavPlugInRGBColor& color1,
                                    const TriglavPlugInRGBColor& color2,
                                    float t) {
        LabColor lab1 = ColorSpaceConverter::RGBtoLab(color1);
        LabColor lab2 = ColorSpaceConverter::RGBtoLab(color2);
        
        LabColor result;
        result.L = lab1.L + t * (lab2.L - lab1.L);
        result.a = lab1.a + t * (lab2.a - lab1.a);
        result.b = lab1.b + t * (lab2.b - lab1.b);
        
    return ColorSpaceConverter::LabtoRGB(result);
}

float ColorGradient::Calculate2DPosition(int x, int y, int width, int height, float centerX, float centerY) {
        float fx = static_cast<float>(x) / (width - 1);
        float fy = static_cast<float>(y) / (height - 1);
        
        switch (gradientType) {
            case GRADIENT_LINEAR:
                return fx; // 水平线性渐变
                
            case GRADIENT_RADIAL: {
                float dx = fx - centerX;
                float dy = fy - centerY;
                float distance = sqrt(dx * dx + dy * dy);
                return std::min(1.0f, distance * 2.0f); // 归一化到半径
            }
            
            case GRADIENT_ANGULAR: {
                float dx = fx - centerX;
                float dy = fy - centerY;
                float angle = atan2(dy, dx);
                return (angle + M_PI) / (2.0f * M_PI); // 归一化到0-1
            }
            
            case GRADIENT_DIAMOND: {
                float dx = abs(fx - centerX);
                float dy = abs(fy - centerY);
                return std::min(1.0f, (dx + dy) * 2.0f);
            }
            
            default:
            return fx;
    }
}

ColorGradient PresetGradients::CreateRainbow() {
        ColorGradient gradient;
        gradient.SetInterpolationMode(INTERPOLATION_HSB);
        
        TriglavPlugInRGBColor red = {255, 0, 0};
        TriglavPlugInRGBColor orange = {255, 165, 0};
        TriglavPlugInRGBColor yellow = {255, 255, 0};
        TriglavPlugInRGBColor green = {0, 255, 0};
        TriglavPlugInRGBColor blue = {0, 0, 255};
        TriglavPlugInRGBColor indigo = {75, 0, 130};
        TriglavPlugInRGBColor violet = {238, 130, 238};
        
        gradient.AddStop(0.0f, red);
        gradient.AddStop(0.167f, orange);
        gradient.AddStop(0.333f, yellow);
        gradient.AddStop(0.5f, green);
        gradient.AddStop(0.667f, blue);
        gradient.AddStop(0.833f, indigo);
        gradient.AddStop(1.0f, violet);
        
    return gradient;
}

ColorGradient PresetGradients::CreateSunset() {
        ColorGradient gradient;
        gradient.SetInterpolationMode(INTERPOLATION_RGB);
        
        TriglavPlugInRGBColor purple = {147, 39, 143};
        TriglavPlugInRGBColor pink = {233, 30, 99};
        TriglavPlugInRGBColor orange = {255, 152, 0};
        TriglavPlugInRGBColor yellow = {255, 235, 59};
        
        gradient.AddStop(0.0f, purple);
        gradient.AddStop(0.33f, pink);
        gradient.AddStop(0.67f, orange);
        gradient.AddStop(1.0f, yellow);
        
    return gradient;
}

ColorGradient PresetGradients::CreateOcean() {
        ColorGradient gradient;
        gradient.SetInterpolationMode(INTERPOLATION_LAB);
        
        TriglavPlugInRGBColor darkBlue = {0, 119, 190};
        TriglavPlugInRGBColor lightBlue = {0, 180, 216};
        TriglavPlugInRGBColor cyan = {144, 224, 239};
        TriglavPlugInRGBColor white = {255, 255, 255};
        
        gradient.AddStop(0.0f, darkBlue);
        gradient.AddStop(0.33f, lightBlue);
        gradient.AddStop(0.67f, cyan);
        gradient.AddStop(1.0f, white);
        
    return gradient;
}

ColorGradient PresetGradients::CreateFire() {
        ColorGradient gradient;
        gradient.SetInterpolationMode(INTERPOLATION_HSB);
        
        TriglavPlugInRGBColor black = {0, 0, 0};
        TriglavPlugInRGBColor darkRed = {139, 0, 0};
        TriglavPlugInRGBColor red = {255, 0, 0};
        TriglavPlugInRGBColor orange = {255, 165, 0};
        TriglavPlugInRGBColor yellow = {255, 255, 0};
        TriglavPlugInRGBColor white = {255, 255, 255};
        
        gradient.AddStop(0.0f, black);
        gradient.AddStop(0.2f, darkRed);
        gradient.AddStop(0.4f, red);
        gradient.AddStop(0.6f, orange);
        gradient.AddStop(0.8f, yellow);
        gradient.AddStop(1.0f, white);
        
    return gradient;
}
