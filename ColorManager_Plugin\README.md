# CSP 高级颜色管理插件

这是一个为 Clip Studio Paint 开发的高级颜色管理插件，提供专业级的颜色选择、转换和调色板功能。

## 功能特性

### 🎨 多种颜色模式支持
- **RGB模式**: 红绿蓝三基色，适用于数字显示
- **CMYK模式**: 青品黄黑印刷色彩，适用于印刷输出
- **HSB模式**: 色相饱和度亮度，更符合人类视觉感受
- **Lab模式**: 设备无关的颜色空间，精确颜色表示
- **灰度模式**: 256级灰度表现
- **位图模式**: 纯黑白二值图像

### 🌈 颜色和谐理论
- **互补色**: 180度对面的颜色组合
- **邻近色**: 色轮上相邻的颜色
- **三角色**: 120度等间距的三色组合
- **分割互补色**: 150度和210度的颜色组合
- **四角色**: 90度等间距的四色组合

### 🔧 高级颜色控制
- **亮度锁定**: 锁定Lab空间的L值，保持亮度一致
- **色域锁定**: 限制颜色范围，确保调色板一致性
- **RGB/RYB模式**: 支持传统艺术家色轮(RYB)和数字色轮(RGB)
- **饱和度限制**: 控制颜色的饱和度范围

### 🎯 智能调色盘
- **自动生成**: 基于颜色和谐理论自动生成调色盘
- **多槽位支持**: 最多16个颜色槽位
- **排序功能**: 按色相、亮度、饱和度排序
- **导入导出**: 调色盘数据的导入导出
- **实时预览**: 颜色变化的即时反馈

### 📈 专业颜色分析
- **对比度分析**: WCAG 2.1标准对比度计算
- **可访问性检查**: AA/AAA级别可访问性验证
- **颜色盲测试**: 红绿蓝色盲模拟预览
- **色温分析**: 暖色/冷色自动判断

### 🎨 颜色采样器
- **单点采样**: 从画布精确取色
- **区域采样**: 多点采样获取颜色分布
- **智能分析**: 自动分析采样结果

### ⏮️ 颜色历史记录
- **历史追踪**: 最多50个颜色的使用历史
- **撤销重做**: 快速回到之前的颜色
- **智能去重**: 避免重复记录相同颜色

### 🌈 渐变生成器
- **多种类型**: 线性、径向、角度、菱形渐变
- **插值模式**: RGB、HSB、Lab空间插值
- **预设渐变**: 彩虹、日落、海洋、火焰等
- **2D生成**: 支持生成2D渐变图像

## 技术架构

### 核心组件
- `ColorManager`: 主要颜色管理器
- `ColorSpaceConverter`: 颜色空间转换器
- `ColorHarmony`: 颜色和谐生成器
- `ColorConstraints`: 颜色约束管理器
- `ColorPalette`: 调色盘管理器
- `ColorHistory`: 颜色历史记录管理器
- `ColorSampler`: 颜色采样器
- `ColorAnalyzer`: 颜色分析工具
- `ColorGradient`: 渐变生成器

### 颜色空间转换
支持以下颜色空间之间的精确转换：
- RGB ↔ HSB
- RGB ↔ Lab (通过XYZ色彩空间)
- RGB ↔ CMYK
- RGB ↔ RYB (艺术家色轮)
- RGB ↔ 灰度
- RGB ↔ 位图

### 和谐色算法
基于色彩理论实现的专业配色算法：
```cpp
// 互补色: 色相旋转180度
complementary = RotateHue(baseColor, 180°)

// 三角色: 色相旋转120度间隔
triadic = [baseColor, RotateHue(baseColor, 120°), RotateHue(baseColor, 240°)]
```

## 编译说明

### 环境要求
- Visual Studio 2019 或更高版本
- Windows SDK 10.0
- CSP 插件 SDK

### 编译步骤
1. 打开 `ColorManager_Plugin.vcxproj`
2. 确保包含路径指向 CSP SDK 目录
3. 选择 Release 配置
4. 编译生成 DLL 文件

### 安装方法
1. 将编译生成的 `.dll` 文件复制到 CSP 插件目录
2. 重启 Clip Studio Paint
3. 在滤镜菜单中找到颜色管理插件

## 使用说明

### 基本操作
1. **选择颜色模式**: 在下拉菜单中选择 RGB、HSB、CMYK 或 Lab
2. **调整颜色**: 使用对应的滑块调整颜色值
3. **选择和谐类型**: 选择互补色、邻近色等和谐方案
4. **启用约束**: 根据需要启用亮度锁定或色域锁定

### 高级功能
- **亮度锁定**: 勾选后可以保持颜色亮度不变，只调整色相和饱和度
- **RYB模式**: 启用传统艺术家色轮，更适合绘画创作
- **实时预览**: 保持预览开启可以实时看到颜色变化效果

### 历史记录操作
- **撤销**: 点击撤销按钮回到上一个颜色
- **重做**: 点击重做按钮前进到下一个颜色
- **历史浏览**: 查看最近使用的颜色历史

### 调色盘管理
- **排序**: 使用排序按钮按色相、亮度或饱和度排序
- **清空**: 一键清空当前调色盘
- **自动生成**: 基于当前颜色和选定的和谐类型自动生成调色盘

### 颜色分析
- **对比度**: 查看当前颜色与背景色的对比度比值
- **可访问性**: 检查颜色组合是否符合WCAG标准
- **色温**: 查看当前颜色的色温信息
- **颜色盲测试**: 预览颜色在不同类型色盲眼中的效果

## 技术细节

### 颜色空间转换精度
- 使用标准的颜色转换矩阵
- Lab转换通过D65白点标准化
- RYB转换基于艺术家色轮理论

### 性能优化
- 颜色转换使用查找表优化
- UI更新采用增量刷新
- 内存管理遵循RAII原则

### 扩展性设计
- 模块化架构便于添加新功能
- 插件接口标准化
- 支持未来的颜色空间扩展

## 开发者信息

本插件基于 CSP 官方 SDK 开发，遵循插件开发最佳实践。

### 版本历史
- v1.0: 基础颜色管理功能
- v1.1: 添加颜色和谐算法
- v1.2: 增加高级约束功能、历史记录、颜色分析、采样器、渐变生成器

### 许可证
本项目采用 MIT 许可证，详见 LICENSE 文件。
