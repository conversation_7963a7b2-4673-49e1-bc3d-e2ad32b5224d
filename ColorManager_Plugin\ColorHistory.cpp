#include "ColorManager.h"
#include <algorithm>

// 颜色历史记录管理器实现
ColorHistory::ColorHistory(size_t maxSize) 
    : maxHistorySize(maxSize), currentIndex(-1) {
    history.reserve(maxSize);
}

void ColorHistory::AddColor(const TriglavPlugInRGBColor& color) {
    // 检查是否与当前颜色相同
    if (!history.empty() && currentIndex >= 0 && currentIndex < static_cast<int>(history.size())) {
        const auto& currentColor = history[currentIndex];
        if (currentColor.red == color.red && 
            currentColor.green == color.green && 
            currentColor.blue == color.blue) {
            return; // 相同颜色不重复添加
        }
    }
    
    // 如果当前不在历史末尾，删除后面的历史
    if (currentIndex >= 0 && currentIndex < static_cast<int>(history.size()) - 1) {
        history.erase(history.begin() + currentIndex + 1, history.end());
    }
    
    // 添加新颜色
    history.push_back(color);
    currentIndex = static_cast<int>(history.size()) - 1;
    
    // 如果超过最大历史数量，删除最旧的
    if (history.size() > maxHistorySize) {
        history.erase(history.begin());
        currentIndex--;
    }
}

bool ColorHistory::CanUndo() const {
    return currentIndex > 0;
}

bool ColorHistory::CanRedo() const {
    return currentIndex >= 0 && currentIndex < static_cast<int>(history.size()) - 1;
}

TriglavPlugInRGBColor ColorHistory::Undo() {
    if (CanUndo()) {
        currentIndex--;
        return history[currentIndex];
    }
    
    // 返回黑色作为默认值
    TriglavPlugInRGBColor black = {0, 0, 0};
    return black;
}

TriglavPlugInRGBColor ColorHistory::Redo() {
    if (CanRedo()) {
        currentIndex++;
        return history[currentIndex];
    }
    
    // 返回黑色作为默认值
    TriglavPlugInRGBColor black = {0, 0, 0};
    return black;
}

TriglavPlugInRGBColor ColorHistory::GetCurrentColor() const {
    if (currentIndex >= 0 && currentIndex < static_cast<int>(history.size())) {
        return history[currentIndex];
    }
    
    // 返回白色作为默认值
    TriglavPlugInRGBColor white = {255, 255, 255};
    return white;
}

void ColorHistory::Clear() {
    history.clear();
    currentIndex = -1;
}

TriglavPlugInRGBColor ColorHistory::GetHistoryColor(size_t index) const {
    if (index < history.size()) {
        return history[index];
    }
    
    // 返回黑色作为默认值
    TriglavPlugInRGBColor black = {0, 0, 0};
    return black;
}

// 颜色采样器实现
TriglavPlugInRGBColor ColorSampler::SampleFromPoint(
    TriglavPlugInOffscreenObject offscreen,
    TriglavPlugInOffscreenService* offscreenService,
    const TriglavPlugInPoint& point) {
    
    if (offscreen == nullptr || offscreenService == nullptr) {
        TriglavPlugInRGBColor black = {0, 0, 0};
        return black;
    }
    
    // 获取点的颜色数据
    TriglavPlugInRect singlePixel;
    singlePixel.left = point.x;
    singlePixel.top = point.y;
    singlePixel.right = point.x + 1;
    singlePixel.bottom = point.y + 1;
    
    TriglavPlugInPtr address;
    TriglavPlugInInt rowBytes, pixelBytes;
    TriglavPlugInRect blockRect;
    
    TriglavPlugInInt result = offscreenService->getBlockImageProc(
        offscreen, &point, &address, &rowBytes, &pixelBytes, &blockRect);
    
    if (result == kTriglavPlugInAPIResultSuccess && address != nullptr) {
        // 假设是RGBA格式
        unsigned char* pixelData = static_cast<unsigned char*>(address);
        
        TriglavPlugInRGBColor color;
        color.red = pixelData[0];
        color.green = pixelData[1];
        color.blue = pixelData[2];
        // Alpha通道在pixelData[3]，这里忽略
        
        return color;
    }
    
    // 采样失败，返回黑色
    TriglavPlugInRGBColor black = {0, 0, 0};
    return black;
}

std::vector<TriglavPlugInRGBColor> ColorSampler::SampleFromArea(
    TriglavPlugInOffscreenObject offscreen,
    TriglavPlugInOffscreenService* offscreenService,
    const TriglavPlugInRect& area,
    int sampleCount) {
    
    std::vector<TriglavPlugInRGBColor> samples;
    
    if (offscreen == nullptr || offscreenService == nullptr || sampleCount <= 0) {
        return samples;
    }
    
    int width = area.right - area.left;
    int height = area.bottom - area.top;
    
    if (width <= 0 || height <= 0) {
        return samples;
    }
    
    // 计算采样点
    int samplesPerRow = static_cast<int>(sqrt(sampleCount));
    int samplesPerCol = (sampleCount + samplesPerRow - 1) / samplesPerRow;
    
    for (int row = 0; row < samplesPerCol; ++row) {
        for (int col = 0; col < samplesPerRow && samples.size() < static_cast<size_t>(sampleCount); ++col) {
            TriglavPlugInPoint samplePoint;
            samplePoint.x = area.left + (col * width) / samplesPerRow + width / (2 * samplesPerRow);
            samplePoint.y = area.top + (row * height) / samplesPerCol + height / (2 * samplesPerCol);
            
            TriglavPlugInRGBColor sampledColor = SampleFromPoint(offscreen, offscreenService, samplePoint);
            samples.push_back(sampledColor);
        }
    }
    
    return samples;
}

// 颜色分析工具实现
float ColorAnalyzer::GetRelativeLuminance(const TriglavPlugInRGBColor& color) {
    // 转换到线性RGB
    auto linearize = [](float value) {
        value /= 255.0f;
        if (value <= 0.03928f) {
            return value / 12.92f;
        } else {
            return pow((value + 0.055f) / 1.055f, 2.4f);
        }
    };
    
    float r = linearize(static_cast<float>(color.red));
    float g = linearize(static_cast<float>(color.green));
    float b = linearize(static_cast<float>(color.blue));
    
    // 计算相对亮度 (ITU-R BT.709)
    return 0.2126f * r + 0.7152f * g + 0.0722f * b;
}

float ColorAnalyzer::CalculateContrast(const TriglavPlugInRGBColor& color1, const TriglavPlugInRGBColor& color2) {
    float lum1 = GetRelativeLuminance(color1);
    float lum2 = GetRelativeLuminance(color2);
    
    // 确保较亮的颜色在分子
    if (lum1 < lum2) {
        std::swap(lum1, lum2);
    }
    
    return (lum1 + 0.05f) / (lum2 + 0.05f);
}

bool ColorAnalyzer::IsAccessible(const TriglavPlugInRGBColor& foreground, 
                                const TriglavPlugInRGBColor& background, 
                                bool largeText) {
    float contrast = CalculateContrast(foreground, background);
    
    // WCAG 2.1 AA标准
    float requiredContrast = largeText ? 3.0f : 4.5f;
    
    return contrast >= requiredContrast;
}

// 颜色盲模拟 (简化版本)
TriglavPlugInRGBColor ColorAnalyzer::SimulateProtanopia(const TriglavPlugInRGBColor& color) {
    // 红色盲模拟矩阵
    float r = color.red / 255.0f;
    float g = color.green / 255.0f;
    float b = color.blue / 255.0f;
    
    TriglavPlugInRGBColor result;
    result.red = static_cast<TriglavPlugInUInt8>((0.567f * r + 0.433f * g + 0.0f * b) * 255);
    result.green = static_cast<TriglavPlugInUInt8>((0.558f * r + 0.442f * g + 0.0f * b) * 255);
    result.blue = static_cast<TriglavPlugInUInt8>((0.0f * r + 0.242f * g + 0.758f * b) * 255);
    
    return result;
}

TriglavPlugInRGBColor ColorAnalyzer::SimulateDeuteranopia(const TriglavPlugInRGBColor& color) {
    // 绿色盲模拟矩阵
    float r = color.red / 255.0f;
    float g = color.green / 255.0f;
    float b = color.blue / 255.0f;
    
    TriglavPlugInRGBColor result;
    result.red = static_cast<TriglavPlugInUInt8>((0.625f * r + 0.375f * g + 0.0f * b) * 255);
    result.green = static_cast<TriglavPlugInUInt8>((0.7f * r + 0.3f * g + 0.0f * b) * 255);
    result.blue = static_cast<TriglavPlugInUInt8>((0.0f * r + 0.3f * g + 0.7f * b) * 255);
    
    return result;
}

TriglavPlugInRGBColor ColorAnalyzer::SimulateTritanopia(const TriglavPlugInRGBColor& color) {
    // 蓝色盲模拟矩阵
    float r = color.red / 255.0f;
    float g = color.green / 255.0f;
    float b = color.blue / 255.0f;
    
    TriglavPlugInRGBColor result;
    result.red = static_cast<TriglavPlugInUInt8>((0.95f * r + 0.05f * g + 0.0f * b) * 255);
    result.green = static_cast<TriglavPlugInUInt8>((0.0f * r + 0.433f * g + 0.567f * b) * 255);
    result.blue = static_cast<TriglavPlugInUInt8>((0.0f * r + 0.475f * g + 0.525f * b) * 255);
    
    return result;
}

float ColorAnalyzer::CalculateColorTemperature(const TriglavPlugInRGBColor& color) {
    // 简化的色温计算
    float r = color.red / 255.0f;
    float g = color.green / 255.0f;
    float b = color.blue / 255.0f;
    
    // 基于红蓝比例估算色温
    float ratio = (r + 0.1f) / (b + 0.1f);
    
    // 映射到开尔文温度 (近似值)
    if (ratio > 1.0f) {
        return 2000.0f + (ratio - 1.0f) * 3000.0f; // 暖色调
    } else {
        return 6500.0f + (1.0f - ratio) * 3500.0f; // 冷色调
    }
}

bool ColorAnalyzer::IsWarmColor(const TriglavPlugInRGBColor& color) {
    return CalculateColorTemperature(color) < 5000.0f;
}

bool ColorAnalyzer::IsCoolColor(const TriglavPlugInRGBColor& color) {
    return CalculateColorTemperature(color) > 6500.0f;
}
